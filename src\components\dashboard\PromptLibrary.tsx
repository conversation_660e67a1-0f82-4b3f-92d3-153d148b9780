
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";

interface PromptLibraryProps {
  userPlan: any;
}

export const PromptLibrary = ({ userPlan }: PromptLibraryProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    { value: "all", label: "All Prompts" },
    { value: "hooks", label: "Hooks & Openers" },
    { value: "testimonials", label: "Testimonials" },
    { value: "storytelling", label: "Storytelling" },
    { value: "education", label: "Educational" },
    { value: "cta", label: "Call to Actions" }
  ];

  const prompts = [
    {
      id: 1,
      title: "First Person TikTok Hook",
      category: "hooks",
      description: "Create engaging first person hooks that stop the scroll",
      prompt: "Create a first person hook for <PERSON>ik<PERSON>ok that starts with 'POV:' and relates to [topic]. Make it relatable and creates curiosity. Keep it under 10 words.",
      usageCount: 142,
      rating: 4.8,
      isPro: false
    },
    {
      id: 2,
      title: "Why Based Testimonial",
      category: "testimonials",
      description: "Generate authentic testimonials that explain the 'why'",
      prompt: "Write a testimonial that starts with 'Here's why [product] changed my life:' and includes 3 specific benefits with emotional impact. Make it feel genuine and conversational.",
      usageCount: 89,
      rating: 4.6,
      isPro: false
    },
    {
      id: 3,
      title: "Story Format Ad",
      category: "storytelling",
      description: "Transform product features into compelling stories",
      prompt: "Turn this product into a story format ad: Start with a relatable problem, introduce the solution naturally, show the transformation, and end with social proof. Keep it under 200 words.",
      usageCount: 234,
      rating: 4.9,
      isPro: true
    },
    {
      id: 4,
      title: "Educational How To",
      category: "education",
      description: "Create step by step educational content",
      prompt: "Create a 'How to [achieve result] in [timeframe]' post with 3 to 5 actionable steps. Include a hook, clear steps with explanations, and a CTA to learn more.",
      usageCount: 167,
      rating: 4.7,
      isPro: false
    }
  ];

  const filteredPrompts = prompts.filter(prompt => {
    const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         prompt.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || prompt.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const usePrompt = (prompt: any) => {
    // Copy prompt to clipboard and show toast
    navigator.clipboard.writeText(prompt.prompt);
  };

  const isPremiumFeature = (feature: string) => {
    return userPlan?.key === 'starter' && feature === 'premium';
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          🧠 Prompt Library
        </h1>
        <p className="text-gray-600">
          Pre-built prompts for 20+ use cases - take full control of your AI outputs
        </p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search prompts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md bg-white"
        >
          {categories.map(category => (
            <option key={category.value} value={category.value}>{category.label}</option>
          ))}
        </select>
      </div>

      {/* Prompt Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {filteredPrompts.map((prompt) => (
          <Card key={prompt.id} className={prompt.isPro ? "border-purple-200" : ""}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg">{prompt.title}</CardTitle>
                {prompt.isPro && (
                  <Badge className="bg-purple-100 text-purple-800">Pro</Badge>
                )}
              </div>
              <p className="text-gray-600 text-sm">{prompt.description}</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm font-mono text-gray-700">{prompt.prompt}</p>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Used {prompt.usageCount} times</span>
                  <div className="flex items-center">
                    <MaterialIcon name="circle-check" size={16} className="text-yellow-500 mr-1" />
                    <span>{prompt.rating}/5</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button 
                    onClick={() => usePrompt(prompt)}
                    className="flex-1"
                    disabled={prompt.isPro && isPremiumFeature('premium')}
                  >
                    <MaterialIcon name="file-plus" size={16} className="mr-2" />
                    Use Prompt
                  </Button>
                  <Button variant="outline" size="sm">
                    <MaterialIcon name="file-plus" size={16} />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Custom Prompt Builder - Pro Feature */}
      {userPlan?.key !== 'starter' && (
        <Card className="border-green-200">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MaterialIcon name="settings" size={24} className="text-green-600 mr-2" />
              Custom Prompt Builder
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600 text-sm">
                Create and save your own custom prompts with variables and reusable templates.
              </p>
              <div className="flex space-x-2">
                <Button className="bg-green-600 hover:bg-green-700">
                  <MaterialIcon name="circle-plus" size={16} className="mr-2" />
                  Create Custom Prompt
                </Button>
                <Button variant="outline">
                  <MaterialIcon name="file-text" size={16} className="mr-2" />
                  My Saved Prompts
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
