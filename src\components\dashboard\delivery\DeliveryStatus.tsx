
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";

export const DeliveryStatus = () => {
  return (
    <Card className="border-green-200 bg-green-50/50">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center text-lg">
          <MaterialIcon name="circle-check" size={20} className="text-green-600 mr-2" />
          Delivery Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between py-2">
          <span className="text-sm font-medium text-gray-700">Auto-delivery:</span>
          <Badge className="bg-green-600 text-white text-xs">Active</Badge>
        </div>
        <div className="flex items-center justify-between py-2">
          <span className="text-sm font-medium text-gray-700">Next delivery:</span>
          <span className="text-sm font-semibold text-gray-900">Tomorrow at 9:00 AM</span>
        </div>
        <div className="flex items-center justify-between py-2">
          <span className="text-sm font-medium text-gray-700">Content types:</span>
          <span className="text-sm text-gray-900">Scripts, Hooks, Ideas</span>
        </div>
      </CardContent>
    </Card>
  );
};
