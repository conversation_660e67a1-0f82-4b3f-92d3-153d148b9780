
import { Badge } from "@/components/ui/badge";

interface BillingToggleProps {
  billingCycle: "monthly" | "yearly";
  onToggle: (cycle: "monthly" | "yearly") => void;
}

export const BillingToggle = ({ billingCycle, onToggle }: BillingToggleProps) => {
  return (
    <div className="flex items-center justify-center mb-8">
      <div className="flex items-center bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => onToggle("monthly")}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            billingCycle === "monthly"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Monthly
        </button>
        <button
          onClick={() => onToggle("yearly")}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            billingCycle === "yearly"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Yearly
          <Badge className="ml-2 bg-green-500 text-white text-xs">Save 20%</Badge>
        </button>
      </div>
    </div>
  );
};
