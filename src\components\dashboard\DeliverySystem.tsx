
import { useState } from "react";
import { DeliveryStatus } from "./delivery/DeliveryStatus";
import { DeliverySettings } from "./delivery/DeliverySettings";
import { IntegrationOptions } from "./delivery/IntegrationOptions";
import { TestDelivery } from "./delivery/TestDelivery";

interface DeliverySystemProps {
  userPlan: any;
}

export const DeliverySystem = ({ userPlan }: DeliverySystemProps) => {
  const [settings, setSettings] = useState({
    deliveryTime: "daily",
    contentTypes: {
      hooks: true,
      scripts: true,
      captions: false,
      ideas: true
    },
    emailTime: "09:00",
    batchMode: false
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleContentTypeChange = (type: string, enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      contentTypes: {
        ...prev.contentTypes,
        [type]: enabled
      }
    }));
  };

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      <div className="mb-8">
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          📤 One Click Delivery System
        </h1>
        <p className="text-gray-600 text-lg">
          Automatically receive your UGC content via email
        </p>
      </div>

      <div className="grid gap-6">
        <DeliveryStatus />

        <DeliverySettings 
          settings={settings}
          userPlan={userPlan}
          onSettingChange={handleSettingChange}
          onContentTypeChange={handleContentTypeChange}
        />

        <IntegrationOptions />

        <TestDelivery />
      </div>
    </div>
  );
};
