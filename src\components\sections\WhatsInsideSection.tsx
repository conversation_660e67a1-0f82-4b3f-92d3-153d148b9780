
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { MaterialIcon } from "@/components/MaterialIcon";

export const WhatsInsideSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const features = [
    "30 Viral Script Templates for TikTok, Reels, Shorts, Paid Ads",
    "30 Scroll-Stopping Social Hooks",
    "Daily Idea Generator (delivered via Gmail using n8n workflows)",
    "Plug-and-Play Prompt Library (ChatGPT + OpenAI-ready)",
    "1-Click Content Delivery to Your Inbox",
    "Bonus Canva Templates for Eye-Catching UGC",
    "Fully Automated Workflow (no code, no headache)",
    "NEW: AI Pro Add-On with Custom Domain Tuner"
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900"
        >
          What's Inside UGC Kit Pro
        </motion.h2>
        
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          <Card className="p-8 border-0 shadow-lg">
            <div className="grid md:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div key={index} variants={fadeInUp} className="flex items-start gap-3">
                  <MaterialIcon name="check_circle" className="text-green-500 flex-shrink-0 mt-1" size={20} />
                  <span className="text-gray-700">{feature}</span>
                </motion.div>
              ))}
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};
