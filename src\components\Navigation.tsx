
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { MobileNav } from "@/components/MobileNav";
import { smoothScrollTo } from "@/utils/smoothScroll";
import { AuthModal } from "@/components/AuthModal";
import { useLocation } from "react-router-dom";

interface NavigationProps {
  isMobileNavOpen: boolean;
  onMobileNavToggle: () => void;
}

export const Navigation = ({ isMobileNavOpen, onMobileNavToggle }: NavigationProps) => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalView, setAuthModalView] = useState<"signin" | "register">("signin");
  const location = useLocation();
  const isHomePage = location.pathname === "/";

  const handleOpenAuthModal = (view: "signin" | "register") => {
    setAuthModalView(view);
    setIsAuthModalOpen(true);
  };

  return (
    <>
      <nav className="border-b border-gray-100 bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">U</span>
              </div>
              <span className="font-manrope font-bold text-xl text-gray-900">UGC Kit Pro</span>
            </div>
            
            {/* Navigation Links - Show only on homepage */}
            {isHomePage && (
              <div className="hidden md:flex items-center space-x-8">
                <button 
                  onClick={() => smoothScrollTo('features')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Features
                </button>
                <button 
                  onClick={() => smoothScrollTo('pricing')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Pricing
                </button>
                <button 
                  onClick={() => smoothScrollTo('testimonials')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Reviews
                </button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleOpenAuthModal("signin")}
                >
                  Sign In
                </Button>
                <Button 
                  size="sm" 
                  className="bg-green-500 hover:bg-green-600"
                  onClick={() => handleOpenAuthModal("register")}
                >
                  Get Started
                </Button>
              </div>
            )}
            
            <MobileNav isOpen={isMobileNavOpen} onToggle={onMobileNavToggle} />
          </div>
        </div>
      </nav>
      
      {/* Auth Modal */}
      <AuthModal 
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialView={authModalView}
      />
    </>
  );
};
