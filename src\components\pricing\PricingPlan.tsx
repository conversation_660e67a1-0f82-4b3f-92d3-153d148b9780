
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";

interface Plan {
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  description: string;
  features: string[];
}

interface PricingPlanProps {
  planKey: string;
  plan: Plan;
  billingCycle: "monthly" | "yearly";
  isSelected: boolean;
  price: number;
  savings: number;
  onSelect: (planKey: string) => void;
  onSelectPlan: (planKey: string) => void;
}

export const PricingPlan = ({ 
  planKey, 
  plan, 
  billingCycle, 
  isSelected, 
  price, 
  savings, 
  onSelect, 
  onSelectPlan 
}: PricingPlanProps) => {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card
        className={`cursor-pointer transition-all duration-200 ${
          isSelected
            ? "ring-2 ring-green-500 shadow-lg"
            : "hover:shadow-md"
        } ${planKey === "pro" ? "relative" : ""}`}
        onClick={() => onSelect(planKey)}
      >
        {planKey === "pro" && (
          <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-green-500 text-white">
            Most Popular
          </Badge>
        )}
        
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-xl font-manrope font-bold text-gray-900">
            {plan.name}
          </CardTitle>
          <div className="text-3xl font-manrope font-bold text-gray-900">
            ${price}
            {price > 0 && (
              <span className="text-lg font-normal text-gray-600">
                /{billingCycle === "monthly" ? "month" : "year"}
              </span>
            )}
          </div>
          {savings > 0 && (
            <Badge variant="secondary" className="text-green-600">
              Save {savings}%
            </Badge>
          )}
        </CardHeader>

        <CardContent>
          <ul className="space-y-3 mb-6">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-start gap-3 text-gray-700">
                <MaterialIcon name="check_circle" className="text-green-500 flex-shrink-0 mt-0.5" size={16} />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>

          <Button
            className={`w-full ${
              isSelected
                ? "bg-green-500 hover:bg-green-600"
                : "bg-gray-900 hover:bg-gray-800"
            } text-white`}
            onClick={(e) => {
              e.stopPropagation();
              onSelectPlan(planKey);
            }}
          >
            {price === 0 ? "Get Started Free" : "Select Plan"}
          </Button>
        </CardContent>
      </Card>
    </motion.div>
  );
};
