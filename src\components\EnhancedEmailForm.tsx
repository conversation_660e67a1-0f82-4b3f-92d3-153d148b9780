
import { useState } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MaterialIcon } from "@/components/MaterialIcon";
import { toast } from "@/hooks/use-toast";

interface EnhancedEmailFormProps {
  onSubmit: (email: string) => void;
  placeholder?: string;
  buttonText?: string;
  className?: string;
}

export const EnhancedEmailForm = ({ 
  onSubmit, 
  placeholder = "Enter work email",
  buttonText = "Get FREE Pack",
  className = ""
}: EnhancedEmailFormProps) => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);

  const validateEmail = (email: string) => {
    const errors: string[] = [];
    
    if (!email) {
      errors.push("Email is required");
      return errors;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.push("Please enter a valid email address");
    }
    
    if (email.length < 5) {
      errors.push("Email seems too short");
    }
    
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateEmail(email);
    setErrors(validationErrors);
    
    if (validationErrors.length > 0) {
      toast({
        title: "Please fix the following errors:",
        description: validationErrors.join(", "),
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onSubmit(email);
      setEmail("");
      setIsLoading(false);
      toast({
        title: "Success! 🎉",
        description: "Your free starter pack is on its way to your inbox!",
      });
    }, 1500);
  };

  return (
    <form onSubmit={handleSubmit} className={`max-w-md mx-auto ${className}`}>
      <div className="flex flex-col sm:flex-row gap-3 p-2 bg-gray-50 rounded-xl border">
        <div className="flex-1 relative">
          <Input
            type="email"
            placeholder={placeholder}
            value={email}
            onChange={(e) => {
              setEmail(e.target.value);
              if (errors.length > 0) {
                setErrors(validateEmail(e.target.value));
              }
            }}
            className={`border-0 bg-transparent focus-visible:ring-0 text-gray-900 ${
              errors.length > 0 ? "text-red-600" : ""
            }`}
            disabled={isLoading}
          />
          {errors.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute top-full left-0 mt-1 text-xs text-red-500"
            >
              {errors[0]}
            </motion.div>
          )}
        </div>
        <Button 
          type="submit" 
          className="bg-green-500 hover:bg-green-600 text-white px-6 shrink-0 disabled:opacity-50"
          disabled={isLoading}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <MaterialIcon name="refresh" size={16} />
              </motion.div>
              <span>Sending...</span>
            </div>
          ) : (
            buttonText
          )}
        </Button>
      </div>
      
      {email && errors.length === 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center gap-2 text-green-600 text-sm mt-2 justify-center"
        >
          <MaterialIcon name="check_circle" size={16} />
          <span>Email looks good!</span>
        </motion.div>
      )}
    </form>
  );
};
