
import { MaterialIcon } from "@/components/MaterialIcon";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export const EnhancedFooterSection = () => {
  const navigate = useNavigate();

  const socialLinks = [
    { name: "LinkedIn", icon: "business", url: "https://linkedin.com" },
    { name: "Facebook", icon: "facebook", url: "https://facebook.com" },
    { name: "X", icon: "close", url: "https://x.com" }
  ];

  const navigationLinks = [
    { name: "About Us", href: "/about" },
    { name: "Blog", href: "/blog" },
    { name: "Contact Us", href: "/contact" }
  ];

  const policyLinks = [
    { name: "Terms & Conditions", href: "/terms" },
    { name: "Return Policy", href: "/returns" },
    { name: "Privacy Policy", href: "/privacy" }
  ];

  const handleSocialClick = (url: string, platform: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleLinkClick = (href: string) => {
    navigate(href);
  };

  return (
    <footer className="py-12 bg-white border-t border-gray-100">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">U</span>
              </div>
              <span className="font-manrope font-bold text-xl text-gray-900">UGC Kit Pro</span>
            </div>
            <p className="text-gray-600 max-w-md">
              Create viral UGC content in seconds with our AI-powered toolkit. 
              Transform your content strategy and boost engagement effortlessly.
            </p>
          </div>

          {/* Navigation Links */}
          <div>
            <h3 className="font-manrope font-semibold text-gray-900 mb-4">Company</h3>
            <ul className="space-y-2">
              {navigationLinks.map((link) => (
                <li key={link.name}>
                  <button
                    onClick={() => handleLinkClick(link.href)}
                    className="text-gray-600 hover:text-gray-900 transition-colors text-sm"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Policy Links */}
          <div>
            <h3 className="font-manrope font-semibold text-gray-900 mb-4">Legal</h3>
            <ul className="space-y-2">
              {policyLinks.map((link) => (
                <li key={link.name}>
                  <button
                    onClick={() => handleLinkClick(link.href)}
                    className="text-gray-600 hover:text-gray-900 transition-colors text-sm"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Media & Copyright */}
        <div className="flex flex-col md:flex-row items-center justify-between pt-8 border-t border-gray-100">
          {/* Social Icons */}
          <div className="flex items-center space-x-4 mb-4 md:mb-0">
            <span className="text-sm text-gray-600 mr-2">Follow us:</span>
            {socialLinks.map((social) => (
              <Button
                key={social.name}
                variant="ghost"
                size="sm"
                className="p-2 hover:bg-gray-100 rounded-full"
                onClick={() => handleSocialClick(social.url, social.name)}
                title={social.name}
              >
                <MaterialIcon name={social.icon} size={20} className="text-gray-600" />
              </Button>
            ))}
          </div>

          {/* Copyright */}
          <div className="text-sm text-gray-600">
            © 2024 UGC Kit Pro. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};
