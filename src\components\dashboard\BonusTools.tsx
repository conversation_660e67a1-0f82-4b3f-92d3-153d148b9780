
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";

interface BonusToolsProps {
  userPlan: any;
}

export const BonusTools = ({ userPlan }: BonusToolsProps) => {
  const [activeToolTab, setActiveToolTab] = useState("analyzer");

  const tools = [
    {
      id: "analyzer",
      title: "🧪 Hook Analyzer",
      description: "Grades your hook based on emotional triggers + virality potential",
      isPremium: false,
      icon: "search"
    },
    {
      id: "benchmark",
      title: "📊 Script Benchmark Tool",
      description: "Compares your generated script to high-performing UGC formats",
      isPremium: true,
      icon: "circle-check"
    },
    {
      id: "remixer",
      title: "🔄 Content Remixer",
      description: "Rewrites existing content into different formats (video → tweet, etc.)",
      isPremium: true,
      icon: "edit"
    },
    {
      id: "persona",
      title: "🎯 Persona Mapper",
      description: "Helps you define audience tone, pain points, and emotional language",
      isPremium: false,
      icon: "users"
    },
    {
      id: "calendar",
      title: "📈 UGC Calendar",
      description: "Drag-and-drop planner synced with your generated content",
      isPremium: true,
      icon: "calendar"
    }
  ];

  const canvasAssets = [
    {
      title: "TikTok Thumbnails",
      description: "20+ editable templates",
      link: "#"
    },
    {
      title: "Instagram Covers",
      description: "Story & post templates",
      link: "#"
    },
    {
      title: "YouTube Thumbnails",
      description: "Click-worthy designs",
      link: "#"
    }
  ];

  const isPremiumFeature = (isPremium: boolean) => {
    return userPlan?.key === 'starter' && isPremium;
  };

  const renderHookAnalyzer = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">Enter your hook to analyze:</label>
        <textarea
          className="w-full p-3 border border-gray-300 rounded-md"
          rows={3}
          placeholder="e.g., 'Wait, this actually works?' or 'POV: You finally found the solution'"
        />
      </div>
      <Button className="w-full bg-blue-600 hover:bg-blue-700">
        <MaterialIcon name="search" size={16} className="mr-2" />
        Analyze Hook
      </Button>
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-medium mb-2">Analysis will appear here:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Emotional trigger score</li>
          <li>• Curiosity factor rating</li>
          <li>• Virality potential</li>
          <li>• Improvement suggestions</li>
        </ul>
      </div>
    </div>
  );

  const renderPersonaMapper = () => (
    <div className="space-y-4">
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">Target Audience:</label>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="e.g., busy moms, fitness beginners"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Main Pain Point:</label>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="e.g., lack of time, feeling overwhelmed"
          />
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">Emotional Language:</label>
        <textarea
          className="w-full p-3 border border-gray-300 rounded-md"
          rows={2}
          placeholder="Words and phrases your audience resonates with"
        />
      </div>
      <Button className="w-full bg-purple-600 hover:bg-purple-700">
        <MaterialIcon name="users" size={16} className="mr-2" />
        Generate Persona Profile
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          🧰 Bonus Tools & Utilities
        </h1>
        <p className="text-gray-600">
          Advanced tools to optimize and scale your UGC content creation
        </p>
      </div>

      {/* Tools Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tools.map((tool) => (
          <Card 
            key={tool.id}
            className={`cursor-pointer transition-all ${
              activeToolTab === tool.id ? "ring-2 ring-green-500" : ""
            } ${isPremiumFeature(tool.isPremium) ? "opacity-50" : ""}`}
            onClick={() => !isPremiumFeature(tool.isPremium) && setActiveToolTab(tool.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <MaterialIcon name={tool.icon} size={24} className="text-gray-600" />
                {tool.isPremium && (
                  <Badge className="bg-purple-100 text-purple-800">Pro</Badge>
                )}
              </div>
              <CardTitle className="text-base">{tool.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">{tool.description}</p>
              {isPremiumFeature(tool.isPremium) && (
                <Button size="sm" className="w-full mt-3" disabled>
                  Upgrade to Access
                </Button>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Active Tool Display */}
      <Card>
        <CardHeader>
          <CardTitle>
            {tools.find(t => t.id === activeToolTab)?.title || "Select a Tool"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {activeToolTab === "analyzer" && renderHookAnalyzer()}
          {activeToolTab === "persona" && renderPersonaMapper()}
          {(activeToolTab === "benchmark" || activeToolTab === "remixer" || activeToolTab === "calendar") && (
            <div className="text-center py-8">
              <MaterialIcon name="settings" size={48} className="mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600">
                This tool is available in Pro and Ultimate plans
              </p>
              <Button className="mt-4">Upgrade Plan</Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Canva Toolkit */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MaterialIcon name="edit" size={24} className="text-orange-500 mr-2" />
            🎨 Canva Toolkit
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            {canvasAssets.map((asset, index) => (
              <div key={index} className="text-center p-4 border border-gray-200 rounded-lg">
                <h4 className="font-medium mb-2">{asset.title}</h4>
                <p className="text-sm text-gray-600 mb-3">{asset.description}</p>
                <Button size="sm" variant="outline" className="w-full">
                  <MaterialIcon name="edit" size={16} className="mr-2" />
                  Edit in Canva
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
