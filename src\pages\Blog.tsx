
import { Navigation } from "@/components/Navigation";
import { FooterSection } from "@/components/sections/FooterSection";
import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const Blog = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  const blogPosts = [
    {
      title: "10 Viral UGC Templates That Actually Work",
      excerpt: "Discover the exact templates used by top creators to generate millions of views.",
      date: "2024-01-15",
      category: "Templates",
      readTime: "5 min read"
    },
    {
      title: "How AI is Revolutionizing Content Creation",
      excerpt: "The future of content is here. Learn how AI tools are changing the game for creators.",
      date: "2024-01-10",
      category: "AI & Technology",
      readTime: "8 min read"
    },
    {
      title: "From Zero to Viral: A Creator's Journey",
      excerpt: "Real case study: How <PERSON> went from 100 followers to 100K using UGC Kit Pro.",
      date: "2024-01-05",
      category: "Case Study",
      readTime: "6 min read"
    }
  ];

  return (
    <div className="min-h-screen bg-white font-inter">
      <Navigation 
        isMobileNavOpen={isMobileNavOpen} 
        onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)} 
      />

      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-6xl mx-auto"
        >
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-manrope font-bold mb-4 text-gray-900">
              UGC Kit Pro Blog
            </h1>
            <p className="text-xl text-gray-600">
              Tips, strategies, and insights for creating viral content
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-2">
                      <Badge variant="secondary">{post.category}</Badge>
                      <span className="text-sm text-gray-500">{post.readTime}</span>
                    </div>
                    <CardTitle className="text-xl font-manrope font-bold">
                      {post.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{post.excerpt}</p>
                    <p className="text-sm text-gray-500">{post.date}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-16">
            <p className="text-gray-600">
              More blog posts coming soon! Subscribe to our newsletter to stay updated.
            </p>
          </div>
        </motion.div>
      </div>

      <FooterSection />
    </div>
  );
};

export default Blog;
