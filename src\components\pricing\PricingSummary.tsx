
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";

interface Plan {
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  description: string;
  features: string[];
}

interface PricingSummaryProps {
  plans: Record<string, Plan>;
  selectedPlan: string;
  billingCycle: "monthly" | "yearly";
  onSelectPlan: (planKey: string) => void;
}

export const PricingSummary = ({
  plans,
  selectedPlan,
  billingCycle,
  onSelectPlan
}: PricingSummaryProps) => {
  const navigate = useNavigate();

  const getPrice = (planKey: string) => {
    const plan = plans[planKey];
    return billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice;
  };

  const getSavings = (planKey: string) => {
    const plan = plans[planKey];
    if (billingCycle === "yearly" && plan.monthlyPrice > 0) {
      const yearlyEquivalent = plan.monthlyPrice * 12;
      const savings = yearlyEquivalent - plan.yearlyPrice;
      return Math.round(savings / yearlyEquivalent * 100);
    }
    return 0;
  };

  const selectedPlanData = plans[selectedPlan];
  const price = getPrice(selectedPlan);
  const savings = getSavings(selectedPlan);

  const handleSelectPlan = () => {
    if (price === 0) {
      // Free plan - direct to dashboard
      localStorage.setItem('userPlan', JSON.stringify({
        ...selectedPlanData,
        key: selectedPlan,
        price,
        billingCycle
      }));
      navigate("/dashboard");
    } else {
      // Paid plan - go to payment
      navigate("/payment", {
        state: {
          plan: {
            ...selectedPlanData,
            key: selectedPlan,
            price,
            billingCycle,
            features: selectedPlanData.features
          }
        }
      });
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }} 
      animate={{ opacity: 1, y: 0 }} 
      className="mt-8"
    >
      <Card className="p-6 max-w-md mx-auto border-2 border-green-500">
        <div className="text-center">
          <h3 className="text-lg font-manrope font-bold mb-2">Selected Plan</h3>
          <div className="mb-4">
            <h4 className="text-xl font-bold text-gray-900">{selectedPlanData.name}</h4>
            <div className="text-2xl font-bold text-green-600">
              ${price}
              {price > 0 && (
                <span className="text-sm font-normal text-gray-600">
                  /{billingCycle === "monthly" ? "month" : "year"}
                </span>
              )}
            </div>
            {savings > 0 && (
              <Badge variant="secondary" className="text-green-600 mt-2">
                Save {savings}%
              </Badge>
            )}
          </div>
          <Button
            onClick={handleSelectPlan}
            className="w-full bg-green-500 hover:bg-green-600 text-white"
          >
            {price === 0 ? "Get Started Free" : "Continue to Payment"}
          </Button>
        </div>
      </Card>
    </motion.div>
  );
};
