
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { MaterialIcon } from "@/components/MaterialIcon";

export const HowItWorksSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const steps = [
    {
      step: "1",
      title: "Select Content Type",
      description: "Pick a script, ad, or caption. Choose your niche or input your product info.",
      icon: <MaterialIcon name="flash_on" className="text-blue-600" size={32} />
    },
    {
      step: "2",
      title: "AI Generates the Content",
      description: "UGC Kit Pro uses custom ChatGPT prompts + OpenAI fine-tuning to build viral content instantly.",
      icon: <MaterialIcon name="auto_awesome" className="text-blue-600" size={32} />
    },
    {
      step: "3",
      title: "Auto-Deliver or Copy-Paste",
      description: "Send it to your inbox. Post it. Get results. Done.",
      icon: <MaterialIcon name="email" className="text-blue-600" size={32} />
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900"
        >
          How It Works — 3 Simple Steps
        </motion.h2>
        
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {steps.map((item, index) => (
            <motion.div key={index} variants={fadeInUp}>
              <Card className="text-center p-8 h-full border-0 shadow-sm hover:shadow-md transition-shadow">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-2xl font-bold mx-auto mb-4">
                    {item.step}
                  </div>
                  <div className="mb-4">{item.icon}</div>
                </div>
                <h3 className="text-xl font-manrope font-bold mb-4 text-gray-900">{item.title}</h3>
                <p className="text-gray-600 leading-relaxed">{item.description}</p>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};
