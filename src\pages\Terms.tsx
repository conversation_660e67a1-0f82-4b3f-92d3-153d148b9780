
import { Navigation } from "@/components/Navigation";
import { FooterSection } from "@/components/sections/FooterSection";
import { useState } from "react";
import { motion } from "framer-motion";

const Terms = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white font-inter">
      <Navigation 
        isMobileNavOpen={isMobileNavOpen} 
        onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)} 
      />

      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl font-manrope font-bold text-center mb-8 text-gray-900">
            Terms & Conditions
          </h1>
          
          <div className="prose prose-lg mx-auto text-gray-600">
            <p className="text-center text-sm text-gray-500 mb-8">
              Last updated: January 1, 2024
            </p>

            <h2>1. Agreement to Terms</h2>
            <p>
              By accessing and using UGC Kit Pro, you accept and agree to be bound by the terms 
              and provision of this agreement.
            </p>

            <h2>2. Use License</h2>
            <p>
              Permission is granted to temporarily download one copy of UGC Kit Pro materials for 
              personal, non-commercial transitory viewing only. This is the grant of a license, 
              not a transfer of title.
            </p>

            <h2>3. Disclaimer</h2>
            <p>
              The materials on UGC Kit Pro are provided on an 'as is' basis. UGC Kit Pro makes 
              no warranties, expressed or implied, and hereby disclaims and negates all other 
              warranties including without limitation, implied warranties or conditions of 
              merchantability, fitness for a particular purpose, or non-infringement of 
              intellectual property or other violation of rights.
            </p>

            <h2>4. Limitations</h2>
            <p>
              In no event shall UGC Kit Pro or its suppliers be liable for any damages 
              (including, without limitation, damages for loss of data or profit, or due to 
              business interruption) arising out of the use or inability to use the materials 
              on UGC Kit Pro, even if UGC Kit Pro or its authorized representative has been 
              notified orally or in writing of the possibility of such damage.
            </p>

            <h2>5. Accuracy of Materials</h2>
            <p>
              The materials appearing on UGC Kit Pro could include technical, typographical, 
              or photographic errors. UGC Kit Pro does not warrant that any of the materials 
              on its website are accurate, complete, or current.
            </p>

            <h2>6. Links</h2>
            <p>
              UGC Kit Pro has not reviewed all of the sites linked to our website and is not 
              responsible for the contents of any such linked site.
            </p>

            <h2>7. Modifications</h2>
            <p>
              UGC Kit Pro may revise these terms of service at any time without notice. By 
              using this website, you are agreeing to be bound by the then current version 
              of these terms of service.
            </p>

            <h2>8. Governing Law</h2>
            <p>
              These terms and conditions are governed by and construed in accordance with the 
              laws of the United States and you irrevocably submit to the exclusive 
              jurisdiction of the courts in that state or location.
            </p>

            <div className="mt-12 p-6 bg-gray-50 rounded-lg">
              <h3 className="font-manrope font-bold text-gray-900 mb-2">Contact Information</h3>
              <p>
                If you have any questions about these Terms & Conditions, please contact us at:
                <br />
                Email: <EMAIL>
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      <FooterSection />
    </div>
  );
};

export default Terms;
