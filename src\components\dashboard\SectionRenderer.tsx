// components/dashboard/SectionRenderer.tsx
import { ScriptGenerator } from "./ScriptGenerator";
import { DailyIdeaVault } from "./DailyIdeaVault";
import { PromptLibrary } from "./PromptLibrary";
import { ScriptLog } from "./ScriptLog";
import { DeliverySystem } from "./DeliverySystem";
import { BonusTools } from "./BonusTools";
import { AnalyticsDashboard } from "./AnalyticsDashboard";
import { ContentCalendar } from "./ContentCalendar";
import { CompetitorAnalysis } from "./CompetitorAnalysis";

interface Props {
  id: string;
  userPlan: any;
}

export const SectionRenderer = ({ id, userPlan }: Props) => {
  switch (id) {
    case "generator":
      return <ScriptGenerator userPlan={userPlan} />;
    case "ideas":
      return <DailyIdeaVault userPlan={userPlan} />;
    case "prompts":
      return <PromptLibrary userPlan={userPlan} />;
    case "analytics":
      return <AnalyticsDashboard userPlan={userPlan} />;
    case "calendar":
      return <ContentCalendar userPlan={userPlan} />;
    case "competitors":
      return <CompetitorAnalysis userPlan={userPlan} />;
    case "history":
      return <ScriptLog userPlan={userPlan} />;
    case "delivery":
      return <DeliverySystem userPlan={userPlan} />;
    case "tools":
      return <BonusTools userPlan={userPlan} />;
    default:
      return <ScriptGenerator userPlan={userPlan} />;
  }
};
