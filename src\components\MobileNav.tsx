
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";

interface MobileNavProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const MobileNav = ({ isOpen, onToggle }: MobileNavProps) => {
  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="sm"
        className="md:hidden"
        onClick={onToggle}
      >
        <MaterialIcon name={isOpen ? "close" : "menu"} size={24} />
      </Button>

      {/* Mobile menu overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 md:hidden"
            onClick={onToggle}
          >
            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 20, stiffness: 100 }}
              className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-6 border-b">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">U</span>
                  </div>
                  <span className="font-manrope font-bold text-xl text-gray-900">UGC Kit Pro</span>
                </div>
                <Button variant="ghost" size="sm" onClick={onToggle}>
                  <MaterialIcon name="close" size={24} />
                </Button>
              </div>
              
              <nav className="p-6">
                <div className="space-y-6">
                  <a 
                    href="#features" 
                    className="block text-lg text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={onToggle}
                  >
                    Features
                  </a>
                  <a 
                    href="#pricing" 
                    className="block text-lg text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={onToggle}
                  >
                    Pricing
                  </a>
                  <a 
                    href="#testimonials" 
                    className="block text-lg text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={onToggle}
                  >
                    Reviews
                  </a>
                  <div className="pt-4 space-y-3">
                    <Button variant="outline" className="w-full">Sign In</Button>
                    <Button className="w-full bg-green-500 hover:bg-green-600">Get Started</Button>
                  </div>
                </div>
              </nav>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
