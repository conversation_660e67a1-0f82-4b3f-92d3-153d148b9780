// components/dashboard/Header.tsx
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";

interface Props {
  userPlan: any;
  userData: any;
  onLogout: () => void;
  getPlanColor: () => string;
}

export const Header = ({ userPlan, userData, onLogout, getPlanColor }: Props) => (
  <header className="bg-white border-b border-gray-200 sticky top-0 z-50 flex-shrink-0">
    <div className="container mx-auto px-4 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">U</span>
          </div>
          <div className="flex flex-col">
            <span className="font-manrope font-bold text-xl text-gray-900">UGC Kit Pro</span>
            <span className="text-sm text-gray-500 hidden md:block">Personal UGC Command Center</span>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {userPlan && (
            <Badge className={`${getPlanColor()} text-white text-xs px-3 py-1 font-medium`}>
              {userPlan.name}
            </Badge>
          )}
          <span className="text-gray-600 hidden md:block font-medium">
            Welcome, {userData?.fullName || "User"}!
          </span>
          <Button variant="outline" onClick={onLogout} size="sm">
            <MaterialIcon name="log-out" size={16} className="mr-2" />
            Logout
          </Button>
        </div>
      </div>
    </div>
  </header>
);
