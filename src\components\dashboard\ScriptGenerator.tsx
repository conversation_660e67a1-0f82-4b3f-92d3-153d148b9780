
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { ScriptGeneratorForm } from "./ScriptGeneratorForm";
import { ScriptGeneratorOutput } from "./ScriptGeneratorOutput";
import { ScriptFormData } from "./ScriptGeneratorTypes";

interface ScriptGeneratorProps {
  userPlan: any;
}

export const ScriptGenerator = ({ userPlan }: ScriptGeneratorProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedScript, setGeneratedScript] = useState("");
  const [formData, setFormData] = useState<ScriptFormData>({
    contentType: "video-script",
    niche: "",
    productDescription: "",
    tone: "casual",
    platform: "tiktok"
  });

  const handleGenerate = async () => {
    setIsGenerating(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const sampleScript = `🎬 Hook: "Wait, this actually works?"

📝 Main Content:
So I tried this [product] for 30 days and honestly? I was skeptical at first. But the results speak for themselves.

Here's what happened:
• Day 1-7: Started noticing small changes
• Day 14: Friends started asking what I was doing differently  
• Day 30: Complete transformation

The best part? It only takes 5 minutes a day.

🎯 CTA: Link in bio to try it yourself - but hurry, they're offering 30% off this week only!

#UGC #Transformation #${formData.platform}`;

      setGeneratedScript(sampleScript);

      const scriptLog = JSON.parse(localStorage.getItem('scriptLog') || '[]');
      scriptLog.push({
        id: Date.now(),
        title: `${formData.contentType} - ${formData.niche}`,
        content: sampleScript,
        type: formData.contentType,
        platform: formData.platform,
        date: new Date().toISOString(),
        saved: true
      });
      localStorage.setItem('scriptLog', JSON.stringify(scriptLog));

      toast({
        title: "Script Generated!",
        description: "Your UGC script has been created and saved to your log."
      });
    } catch (error) {
      toast({
        title: "Generation Failed",
        description: "Please try again in a moment.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          🎬 Script Generator
        </h1>
        <p className="text-gray-600">
          Generate ready-to-use UGC scripts using AI-powered prompts
        </p>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        <ScriptGeneratorForm
          formData={formData}
          isGenerating={isGenerating}
          onFormDataChange={setFormData}
          onGenerate={handleGenerate}
        />
        
        <ScriptGeneratorOutput generatedScript={generatedScript} />
      </div>
    </div>
  );
};
