
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";

interface DeliverySettingsProps {
  settings: {
    deliveryTime: string;
    contentTypes: {
      hooks: boolean;
      scripts: boolean;
      captions: boolean;
      ideas: boolean;
    };
    emailTime: string;
    batchMode: boolean;
  };
  userPlan: any;
  onSettingChange: (key: string, value: any) => void;
  onContentTypeChange: (type: string, enabled: boolean) => void;
}

export const DeliverySettings = ({ 
  settings, 
  userPlan, 
  onSettingChange, 
  onContentTypeChange 
}: DeliverySettingsProps) => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Delivery Schedule</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Frequency</label>
            <select
              value={settings.deliveryTime}
              onChange={(e) => onSettingChange('deliveryTime', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md bg-white"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Delivery Time</label>
            <input
              type="time"
              value={settings.emailTime}
              onChange={(e) => onSettingChange('emailTime', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>

          {userPlan?.key !== 'starter' && (
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={settings.batchMode}
                  onChange={(e) => onSettingChange('batchMode', e.target.checked)}
                  className="mr-2"
                />
                <span className="text-sm">Auto-batching (Pro+ only)</span>
              </label>
              <p className="text-xs text-gray-500 mt-1">
                Receive full UGC packs with multiple scripts
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Content Types</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries(settings.contentTypes).map(([type, enabled]) => (
            <label key={type} className="flex items-center">
              <input
                type="checkbox"
                checked={enabled}
                onChange={(e) => onContentTypeChange(type, e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm capitalize">{type}</span>
            </label>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};
