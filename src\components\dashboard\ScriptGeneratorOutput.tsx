
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { toast } from "@/hooks/use-toast";

interface ScriptGeneratorOutputProps {
  generatedScript: string;
}

export const ScriptGeneratorOutput = ({ generatedScript }: ScriptGeneratorOutputProps) => {
  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(generatedScript);
    toast({
      title: "Copied!",
      description: "Script copied to clipboard."
    });
  };

  const handleSendToGmail = () => {
    const subject = `UGC Script`;
    const body = encodeURIComponent(generatedScript);
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  return (
    <Card className="h-fit">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Generated Script</CardTitle>
      </CardHeader>
      <CardContent>
        {generatedScript ? (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg min-h-64 border border-gray-200">
              <pre className="whitespace-pre-wrap text-sm font-mono leading-relaxed text-gray-800">{generatedScript}</pre>
            </div>
            <div className="flex space-x-3">
              <Button 
                onClick={handleCopyToClipboard}
                variant="outline"
                className="flex-1 justify-center"
              >
                <MaterialIcon name="file-plus" size={16} className="mr-2" />
                Copy
              </Button>
              <Button 
                onClick={handleSendToGmail}
                variant="outline"
                className="flex-1 justify-center"
              >
                <MaterialIcon name="mail" size={16} className="mr-2" />
                Email
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 py-12">
            <MaterialIcon name="edit" size={48} className="mx-auto mb-4 opacity-50 text-gray-400" />
            <p className="font-medium text-gray-600">Your generated script will appear here</p>
            <p className="text-sm text-gray-500 mt-1">Fill out the form and click generate to start</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
