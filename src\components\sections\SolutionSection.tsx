
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";

export const SolutionSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2 variants={fadeInUp} className="text-4xl md:text-5xl font-manrope font-bold mb-6">
            Introducing{" "}
            <span className="bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">
              UGC Kit Pro™
            </span>
          </motion.h2>
          <motion.p variants={fadeInUp} className="text-xl mb-8 max-w-4xl mx-auto text-gray-300">
            The plug-and-play UGC system that creates viral-ready video scripts, captions, and hooks using automated AI workflows — no writing, no tech, no burnout.
          </motion.p>
          <motion.div variants={fadeInUp} className="flex flex-wrap justify-center gap-3 text-sm">
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">ChatGPT</Badge>
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">OpenAI API</Badge>
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">n8n</Badge>
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">Google Sheets</Badge>
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">Gmail</Badge>
            <Badge variant="secondary" className="bg-white/10 text-white border-white/20">Canva</Badge>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
