
import { useState } from "react";
import { motion } from "framer-motion";
import { EnhancedEmailForm } from "@/components/EnhancedEmailForm";
import { AuthModal } from "@/components/AuthModal";

interface FinalCTASectionProps {
  onEmailSubmit: (email: string) => void;
}

export const FinalCTASection = ({ onEmailSubmit }: FinalCTASectionProps) => {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  
  const handleEmailSubmit = (email: string) => {
    onEmailSubmit(email);
    setIsAuthModalOpen(true);
  };

  return (
    <section className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-manrope font-bold mb-6">
            Ready to Build Viral UGC in Seconds?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto text-gray-300">
            Grab your Free Starter Pack — no credit card required.
          </p>
          
          <div className="mb-8">
            <EnhancedEmailForm 
              onSubmit={handleEmailSubmit}
              placeholder="Enter your email"
              buttonText="Send Me the Free Pack"
              className="max-w-md mx-auto"
            />
          </div>
          
          {/* Auth Modal */}
          <AuthModal 
            isOpen={isAuthModalOpen} 
            onClose={() => setIsAuthModalOpen(false)}
            initialView="register"
          />
        </motion.div>
      </div>
    </section>
  );
};
