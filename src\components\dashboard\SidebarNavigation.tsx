// components/dashboard/SidebarNavigation.tsx
import { MaterialIcon } from "@/components/MaterialIcon";

interface SidebarSection {
  id: string;
  label: string;
  icon: string;
}

interface Props {
  sections: SidebarSection[];
  activeSection: string;
  setActiveSection: (id: string) => void;
}

export const SidebarNavigation = ({ sections, activeSection, setActiveSection }: Props) => (
  <aside className="w-64 min-w-[16rem] bg-white border-r border-gray-200 flex-shrink-0 overflow-y-auto">
    <div className="p-4">
      <nav className="space-y-2">
        {sections.map(({ id, label, icon }) => (
          <button
            key={id}
            type="button"
            onClick={() => setActiveSection(id)}
            className={`w-full flex items-center px-4 py-3 rounded-lg text-left text-sm font-medium transition-all duration-200 group ${
              activeSection === id
                ? "bg-green-50 text-green-700 border border-green-200 shadow-sm"
                : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
            }`}
          >
            <div className="flex items-center space-x-3 w-full">
              <MaterialIcon
                name={icon}
                size={18}
                className={`flex-shrink-0 ${
                  activeSection === id ? "text-green-600" : "text-gray-500 group-hover:text-gray-700"
                }`}
              />
              <span className="truncate w-full">{label}</span>
            </div>
          </button>
        ))}
      </nav>
    </div>
  </aside>
);
