
import { useState } from "react";
import { Navigation } from "@/components/Navigation";
import { HeroSection } from "@/components/sections/HeroSection";
import { StatsSection } from "@/components/sections/StatsSection";
import { ProblemSection } from "@/components/sections/ProblemSection";
import { SolutionSection } from "@/components/sections/SolutionSection";
import { FeatureShowcase } from "@/components/FeatureShowcase";
import { HowItWorksSection } from "@/components/sections/HowItWorksSection";
import { WhatsInsideSection } from "@/components/sections/WhatsInsideSection";
import { TestimonialsSection } from "@/components/sections/TestimonialsSection";
import { PricingCalculator } from "@/components/PricingCalculator";
import { FAQSection } from "@/components/sections/FAQSection";
import { GuaranteeSection } from "@/components/sections/GuaranteeSection";
import { FinalCTASection } from "@/components/sections/FinalCTASection";
import { FooterSection } from "@/components/sections/FooterSection";
import { toast } from "@/hooks/use-toast";
import { AuthModal } from "@/components/AuthModal";

const Index = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const handleEmailSubmit = (email: string) => {
    console.log("Email submitted:", email);
    toast({
      title: "Thank you!",
      description: "We'll send your free starter pack to this email."
    });
    setIsAuthModalOpen(true);
  };

  return (
    <div className="min-h-screen bg-white font-inter">
      <Navigation 
        isMobileNavOpen={isMobileNavOpen} 
        onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)} 
      />

      <HeroSection onEmailSubmit={handleEmailSubmit} />
      <StatsSection />
      <ProblemSection />
      <SolutionSection />

      {/* Feature Showcase */}
      <section className="py-20 bg-white" id="features">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900">
            See UGC Kit Pro in Action
          </h2>
          <FeatureShowcase />
        </div>
      </section>

      <HowItWorksSection />
      <WhatsInsideSection />
      <TestimonialsSection />

      {/* Enhanced Pricing */}
      <section className="py-20 bg-white" id="pricing">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900">
            Choose Your Plan
          </h2>
          <PricingCalculator />
        </div>
      </section>

      <FAQSection />
      <GuaranteeSection />
      <FinalCTASection onEmailSubmit={handleEmailSubmit} />
      <FooterSection />
      
      {/* Global Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialView="register"
      />
    </div>
  );
};

export default Index;
