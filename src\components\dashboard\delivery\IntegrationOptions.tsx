
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";

export const IntegrationOptions = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Integration Options</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="text-center p-4 border border-gray-200 rounded-lg">
            <MaterialIcon name="mail" size={32} className="mx-auto mb-2 text-green-600" />
            <h3 className="font-medium mb-1">Gmail</h3>
            <Badge className="bg-green-100 text-green-800 mb-2">Connected</Badge>
            <p className="text-xs text-gray-600">Automated delivery via n8n</p>
          </div>

          <div className="text-center p-4 border border-gray-200 rounded-lg opacity-50">
            <MaterialIcon name="file-text" size={32} className="mx-auto mb-2 text-gray-400" />
            <h3 className="font-medium mb-1">Notion</h3>
            <Badge variant="outline" className="mb-2">Coming Soon</Badge>
            <p className="text-xs text-gray-600">Direct database sync</p>
          </div>

          <div className="text-center p-4 border border-gray-200 rounded-lg opacity-50">
            <MaterialIcon name="bell" size={32} className="mx-auto mb-2 text-gray-400" />
            <h3 className="font-medium mb-1">Slack</h3>
            <Badge variant="outline" className="mb-2">Coming Soon</Badge>
            <p className="text-xs text-gray-600">Team notifications</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
