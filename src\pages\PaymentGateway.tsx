
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";
import { toast } from "@/hooks/use-toast";

const PaymentGateway = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedPlan, setSelectedPlan] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    // Get plan data from navigation state
    const planData = location.state?.plan;
    if (planData) {
      setSelectedPlan(planData);
    } else {
      // Redirect back if no plan selected
      navigate("/");
    }
  }, [location.state, navigate]);

  const handlePayment = async () => {
    setIsProcessing(true);
    
    // Simulate Razorpay payment processing
    try {
      // Mock Razorpay integration
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Payment Successful!",
        description: `Successfully purchased ${selectedPlan?.name}`
      });

      // Store plan info in localStorage for dashboard customization
      localStorage.setItem('userPlan', JSON.stringify(selectedPlan));
      
      // Navigate to register page after successful payment
      navigate("/register", { 
        state: { 
          fromPayment: true, 
          plan: selectedPlan 
        } 
      });
      
    } catch (error) {
      toast({
        title: "Payment Failed",
        description: "Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!selectedPlan) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <MaterialIcon name="error" size={48} className="text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No Plan Selected</h2>
          <p className="text-gray-600 mb-4">Please select a plan from our pricing page.</p>
          <Button onClick={() => navigate("/")} className="bg-green-500 hover:bg-green-600">
            Go Back to Pricing
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-manrope font-bold text-gray-900 mb-4">
            Complete Your Purchase
          </h1>
          <p className="text-gray-600">
            You're one step away from accessing UGC Kit Pro
          </p>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* Plan Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Order Summary</span>
                <Badge className="bg-green-500 text-white">
                  {selectedPlan.billingCycle === "yearly" ? "Save 20%" : "Monthly"}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{selectedPlan.name}</h3>
                    <p className="text-gray-600 text-sm">{selectedPlan.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      ${selectedPlan.price}
                    </div>
                    <div className="text-sm text-gray-600">
                      {selectedPlan.billingCycle === "yearly" ? "/year" : "/month"}
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">Included features:</h4>
                  <ul className="space-y-1">
                    {selectedPlan.features?.map((feature: string, index: number) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <MaterialIcon name="check" size={16} className="text-green-500 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Method */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Method</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3 p-4 border rounded-lg bg-blue-50 border-blue-200">
                <MaterialIcon name="payment" size={24} className="text-blue-600" />
                <div>
                  <div className="font-medium">Razorpay Secure Payment</div>
                  <div className="text-sm text-gray-600">
                    Pay securely with credit card, debit card, or UPI
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Actions */}
          <div className="space-y-4">
            <Button
              onClick={handlePayment}
              disabled={isProcessing}
              className="w-full bg-green-500 hover:bg-green-600 py-3 text-lg"
            >
              {isProcessing ? (
                <div className="flex items-center">
                  <MaterialIcon name="hourglass_empty" size={20} className="mr-2 animate-spin" />
                  Processing Payment...
                </div>
              ) : (
                <div className="flex items-center">
                  <MaterialIcon name="lock" size={20} className="mr-2" />
                  Pay ${selectedPlan.price} Securely
                </div>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate("/")}
              className="w-full"
              disabled={isProcessing}
            >
              Back to Pricing
            </Button>
          </div>

          {/* Security Notice */}
          <div className="text-center text-sm text-gray-500">
            <MaterialIcon name="security" size={16} className="inline mr-1" />
            Your payment information is encrypted and secure
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PaymentGateway;
