
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";

export const ProblemSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const problems = [
    { icon: "📄", text: "Blank pages" },
    { icon: "⏰", text: "No time to write" },
    { icon: "📉", text: "Content that flops" },
    { icon: "💸", text: "Overpriced copywriters" }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2 variants={fadeInUp} className="text-4xl md:text-5xl font-manrope font-bold mb-6 text-gray-900">
            Are you stuck in the{" "}
            <span className="text-red-500">"What do I post today?"</span> loop?
          </motion.h2>
          <motion.p variants={fadeInUp} className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto">
            If you're a creator, solopreneur, or UGC freelancer — you know the pain:
          </motion.p>
        </motion.div>

        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto"
        >
          {problems.map((item, index) => (
            <motion.div key={index} variants={fadeInUp}>
              <Card className="text-center p-6 border border-red-100 hover:border-red-200 transition-colors bg-red-50/30">
                <div className="text-4xl mb-4">{item.icon}</div>
                <p className="font-medium text-gray-700">🚫 {item.text}</p>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <p className="text-2xl font-manrope font-bold text-gray-900">
            Let's fix that — <span className="text-green-500">permanently</span>.
          </p>
        </motion.div>
      </div>
    </section>
  );
};
