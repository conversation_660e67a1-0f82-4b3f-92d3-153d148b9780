
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ScriptLogProps {
  userPlan: any;
}

export const ScriptLog = ({ userPlan }: ScriptLogProps) => {
  const [scripts, setScripts] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");

  useEffect(() => {
    // Load scripts from localStorage
    const scriptLog = JSON.parse(localStorage.getItem('scriptLog') || '[]');
    setScripts(scriptLog);
  }, []);

  const filteredScripts = scripts.filter(script => {
    const matchesSearch = script.title.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === "all" || script.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const handleAction = (action: string, script: any) => {
    switch (action) {
      case 'copy':
        navigator.clipboard.writeText(script.content);
        break;
      case 'email':
        const subject = `UGC Script ${script.title}`;
        const body = encodeURIComponent(script.content);
        window.open(`mailto:?subject=${subject}&body=${body}`);
        break;
      case 'download':
        const blob = new Blob([script.content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${script.title}.txt`;
        a.click();
        break;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6 max-w-full">
      <div>
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          ✍️ Script Log & History
        </h1>
        <p className="text-gray-600">
          Keep your best content organized and accessible
        </p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search scripts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
        <div className="w-full md:w-48">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full bg-white border border-gray-300">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent className="bg-white border border-gray-200 shadow-lg z-50">
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="video-script">Video Scripts</SelectItem>
              <SelectItem value="testimonial">Testimonials</SelectItem>
              <SelectItem value="ad-copy">Ad Copy</SelectItem>
              <SelectItem value="instagram-caption">Instagram Captions</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Scripts Table */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Generated Scripts ({filteredScripts.length})</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredScripts.length > 0 ? (
            <div className="space-y-4">
              {filteredScripts.map((script) => (
                <div key={script.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-gray-900 truncate">{script.title}</h3>
                      <div className="flex items-center space-x-2 mt-2 flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                          {script.type.replace('-', ' ')}
                        </Badge>
                        {script.platform && (
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                            {script.platform}
                          </Badge>
                        )}
                        <span className="text-xs text-gray-500">
                          {formatDate(script.date)}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-1 ml-4 flex-shrink-0">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAction('copy', script)}
                        className="h-8 w-8 p-0"
                      >
                        <MaterialIcon name="file-plus" size={14} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAction('email', script)}
                        className="h-8 w-8 p-0"
                      >
                        <MaterialIcon name="mail" size={14} />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleAction('download', script)}
                        className="h-8 w-8 p-0"
                      >
                        <MaterialIcon name="circle-arrow-down" size={14} />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm text-gray-700 line-clamp-3 leading-relaxed">{script.content}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-12">
              <MaterialIcon name="file-text" size={48} className="mx-auto mb-4 opacity-50" />
              <p className="font-medium">No scripts generated yet</p>
              <p className="text-sm">Start creating scripts to see them appear here</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle>Export & Backup</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="w-full justify-start">
              <MaterialIcon name="circle-arrow-down" size={16} className="mr-2" />
              Export All Scripts
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <MaterialIcon name="mail" size={16} className="mr-2" />
              Email Archive
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <MaterialIcon name="file-text" size={16} className="mr-2" />
              Sync to Google Sheets
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
