
export interface ScriptFormData {
  contentType: string;
  niche: string;
  productDescription: string;
  tone: string;
  platform: string;
}

export interface ContentType {
  value: string;
  label: string;
}

export interface Tone {
  value: string;
  label: string;
}

export interface Platform {
  value: string;
  label: string;
}

export const contentTypes: ContentType[] = [
  { value: "video-script", label: "🎥 Video Script" },
  { value: "testimonial", label: "🗣️ Testimonial" },
  { value: "ad-copy", label: "🧾 Ad Copy" },
  { value: "instagram-caption", label: "📸 Instagram Caption" }
];

export const tones: Tone[] = [
  { value: "casual", label: "Casual" },
  { value: "persuasive", label: "Persuasive" },
  { value: "funny", label: "Funny" },
  { value: "urgent", label: "Urgent" },
  { value: "brand-like", label: "Brand-like" }
];

export const platforms: Platform[] = [
  { value: "tiktok", label: "TikTok" },
  { value: "reels", label: "Instagram Reels" },
  { value: "shorts", label: "YouTube Shorts" },
  { value: "facebook", label: "Facebook" },
  { value: "twitter", label: "Twitter/X" }
];
