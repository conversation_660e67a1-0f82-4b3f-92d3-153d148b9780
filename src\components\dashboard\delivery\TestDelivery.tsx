
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";

export const TestDelivery = () => {
  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Test Your Setup</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-600 text-sm leading-relaxed">
          Send a test delivery to make sure everything is working correctly.
        </p>
        <div className="flex justify-end">
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <Button className="bg-green-600 hover:bg-green-700 flex-1 sm:flex-none px-6 py-3">
              <MaterialIcon name="mail" size={16} className="mr-2" />
              Send Test Email
            </Button>
            <Button variant="outline" className="flex-1 sm:flex-none px-6 py-3">
              <MaterialIcon name="settings" size={16} className="mr-2" />
              Configure Automation
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
