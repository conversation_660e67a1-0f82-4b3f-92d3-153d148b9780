
import { PricingPlan } from "./PricingPlan";

interface Plan {
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  description: string;
  features: string[];
}

interface PricingPlansListProps {
  plans: Record<string, Plan>;
  selectedPlan: string;
  billingCycle: "monthly" | "yearly";
  onSelect: (planKey: string) => void;
  onSelectPlan: (planKey: string) => void;
}

export const PricingPlansList = ({ 
  plans, 
  selectedPlan, 
  billingCycle, 
  onSelect, 
  onSelectPlan 
}: PricingPlansListProps) => {
  const getPrice = (planKey: string) => {
    const plan = plans[planKey];
    return billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice;
  };

  const getSavings = (planKey: string) => {
    const plan = plans[planKey];
    if (billingCycle === "yearly" && plan.monthlyPrice > 0) {
      const yearlyEquivalent = plan.monthlyPrice * 12;
      const savings = yearlyEquivalent - plan.yearlyPrice;
      return Math.round((savings / yearlyEquivalent) * 100);
    }
    return 0;
  };

  return (
    <div className="grid md:grid-cols-3 gap-6">
      {Object.entries(plans).map(([key, plan]) => (
        <PricingPlan
          key={key}
          planKey={key}
          plan={plan}
          billingCycle={billingCycle}
          isSelected={selectedPlan === key}
          price={getPrice(key)}
          savings={getSavings(key)}
          onSelect={onSelect}
          onSelectPlan={onSelectPlan}
        />
      ))}
    </div>
  );
};
