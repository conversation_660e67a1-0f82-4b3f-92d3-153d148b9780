
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { BillingToggle } from "./pricing/BillingToggle";
import { PricingPlansList } from "./pricing/PricingPlansList";
import { PricingSummary } from "./pricing/PricingSummary";

export const PricingCalculator = () => {
  const [selectedPlan, setSelectedPlan] = useState("pro");
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("monthly");
  const navigate = useNavigate();

  const plans = {
    starter: {
      name: "FREE Starter Pack",
      monthlyPrice: 0,
      yearlyPrice: 0,
      description: "Try it now",
      features: [
        "5 Viral Script Templates",
        "10 Hooks + 1-Click Delivery",
        "Manual ChatGPT Prompts"
      ]
    },
    pro: {
      name: "Pro UGC Kit",
      monthlyPrice: 47,
      yearlyPrice: 470,
      description: "Most popular",
      features: [
        "Full Template Library",
        "Daily Inbox Content Drops",
        "Automation via Gmail + n8n",
        "Priority Support"
      ]
    },
    ultimate: {
      name: "Ultimate UGC Builder",
      monthlyPrice: 197,
      yearlyPrice: 1970,
      description: "Ultimate power",
      features: [
        "All Pro Features",
        "Full Automation SOPs",
        "API-ready OpenAI Integration",
        "Custom Niche Tuning Engine",
        "Unlimited Prompt Variants"
      ]
    }
  };

  const getPrice = (planKey: keyof typeof plans) => {
    const plan = plans[planKey];
    return billingCycle === "monthly" ? plan.monthlyPrice : plan.yearlyPrice;
  };

  const handleSelectPlan = (planKey: string) => {
    const plan = plans[planKey as keyof typeof plans];
    const price = getPrice(planKey as keyof typeof plans);
    
    if (price === 0) {
      // Free plan - direct to dashboard
      localStorage.setItem('userPlan', JSON.stringify({
        ...plan,
        key: planKey,
        price,
        billingCycle
      }));
      navigate("/dashboard");
    } else {
      // Paid plan - go to payment
      navigate("/payment", {
        state: {
          plan: {
            ...plan,
            key: planKey,
            price,
            billingCycle,
            features: plan.features
          }
        }
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <BillingToggle 
        billingCycle={billingCycle} 
        onToggle={setBillingCycle} 
      />

      <PricingPlansList
        plans={plans}
        selectedPlan={selectedPlan}
        billingCycle={billingCycle}
        onSelect={setSelectedPlan}
        onSelectPlan={handleSelectPlan}
      />

      <PricingSummary
        plans={plans}
        selectedPlan={selectedPlan}
        billingCycle={billingCycle}
        onSelectPlan={handleSelectPlan}
      />
    </div>
  );
};
