
import { motion } from "framer-motion";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export const FAQSection = () => {
  const faqs = [
    {
      question: "Do I need ChatGPT Plus?",
      answer: "You can use the free tier or plug in your OpenAI API key for advanced features."
    },
    {
      question: "How fast is the setup?",
      answer: "Under 5 minutes. Includes full video walk-through + support chat."
    },
    {
      question: "Can I customize the AI output?",
      answer: "Yes! Use your product descriptions, brand voice, or fine-tune prompts with our Pro Library."
    },
    {
      question: "What tools does it work with?",
      answer: "ChatGPT, OpenAI, n8n, Gmail, Google Sheets, Canva — or just download and go."
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900"
        >
          Frequently Asked Questions
        </motion.h2>
        
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="max-w-3xl mx-auto"
        >
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="border rounded-lg px-6 bg-white">
                <AccordionTrigger className="text-left font-manrope font-semibold text-gray-900">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </motion.div>
      </div>
    </section>
  );
};
