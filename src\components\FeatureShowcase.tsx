
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";

export const FeatureShowcase = () => {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      title: "AI Script Generator",
      description: "Generate viral TikTok, Reels, and YouTube Shorts scripts in seconds",
      icon: "auto_awesome",
      demo: "🎬 Hook: 'POV: You discovered the secret that changed everything...' → Full 60-second script generated instantly"
    },
    {
      title: "Hook Library",
      description: "30+ proven hooks that grab attention in the first 3 seconds",
      icon: "flash_on",
      demo: "🪝 'This mistake cost me $10,000...' | 'Nobody talks about this...' | 'The truth they don't want you to know...'"
    },
    {
      title: "Daily Content Ideas",
      description: "Never run out of content with automated daily idea delivery",
      icon: "email",
      demo: "📧 Today's Ideas: 'Product review format' | 'Behind-the-scenes setup' | 'Problem → Solution reveal'"
    },
    {
      title: "Brand Voice Tuning",
      description: "Customize AI output to match your unique brand personality",
      icon: "tune",
      demo: "🎯 Casual & Fun → Professional → Educational → Inspiring (AI adapts to your style)"
    }
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <div className="grid lg:grid-cols-2 gap-8 items-center">
        {/* Feature Navigation */}
        <div className="space-y-4">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card
                className={`cursor-pointer transition-all duration-300 ${
                  activeFeature === index
                    ? "ring-2 ring-green-500 bg-green-50 border-green-200"
                    : "hover:shadow-md"
                }`}
                onClick={() => setActiveFeature(index)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-lg ${
                      activeFeature === index ? "bg-green-500 text-white" : "bg-gray-100 text-gray-600"
                    }`}>
                      <MaterialIcon name={feature.icon} size={24} />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-manrope font-bold text-gray-900 mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                    {activeFeature === index && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="text-green-500"
                      >
                        <MaterialIcon name="check_circle" size={20} />
                      </motion.div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Feature Demo */}
        <div className="lg:sticky lg:top-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeFeature}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="p-8 bg-gray-900 text-white border-0">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-green-500 rounded-lg">
                    <MaterialIcon name={features[activeFeature].icon} className="text-white" size={20} />
                  </div>
                  <h4 className="text-xl font-manrope font-bold">
                    {features[activeFeature].title} Demo
                  </h4>
                </div>
                
                <div className="bg-gray-800 rounded-lg p-4 font-mono text-sm mb-6">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-gray-400 text-xs ml-2">UGC Kit Pro</span>
                  </div>
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="text-green-400 leading-relaxed"
                  >
                    {features[activeFeature].demo}
                  </motion.p>
                </div>

                <Button 
                  className="w-full bg-green-500 hover:bg-green-600 text-white"
                  onClick={() => {
                    // Scroll to pricing section
                    document.getElementById('pricing')?.scrollIntoView({ behavior: 'smooth' });
                  }}
                >
                  Try This Feature Now
                  <MaterialIcon name="arrow_forward" className="ml-2" size={16} />
                </Button>
              </Card>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};
