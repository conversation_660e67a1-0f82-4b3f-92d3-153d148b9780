
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

interface CompetitorAnalysisProps {
  userPlan: any;
}

export const CompetitorAnalysis = ({ userPlan }: CompetitorAnalysisProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedNiche, setSelectedNiche] = useState("fitness");
  const [competitors, setCompetitors] = useState<any[]>([]);
  const [trendingContent, setTrendingContent] = useState<any[]>([]);

  useEffect(() => {
    // Mock competitor data
    const mockCompetitors = [
      {
        id: 1,
        username: "@fitnessguru",
        platform: "TikTok",
        followers: "2.4M",
        avgViews: "150K",
        engagementRate: 8.2,
        topContent: "Morning workout routines",
        growth: "+15%",
        score: 92
      },
      {
        id: 2,
        username: "@healthylifestyle",
        platform: "Instagram",
        followers: "890K",
        avgViews: "45K",
        engagementRate: 6.7,
        topContent: "Meal prep tutorials",
        growth: "+8%",
        score: 78
      },
      {
        id: 3,
        username: "@workoutmotivation",
        platform: "YouTube",
        followers: "1.2M",
        avgViews: "85K",
        engagementRate: 5.9,
        topContent: "Transformation stories",
        growth: "+12%",
        score: 85
      }
    ];

    const mockTrending = [
      {
        id: 1,
        hashtag: "#morningroutine",
        posts: "2.4M",
        growth: "+45%",
        platform: "TikTok",
        avgViews: "120K"
      },
      {
        id: 2,
        hashtag: "#healthylifestyle",
        posts: "1.8M",
        growth: "+32%",
        platform: "Instagram",
        avgViews: "85K"
      },
      {
        id: 3,
        hashtag: "#fitnessjourney",
        posts: "950K",
        growth: "+28%",
        platform: "TikTok",
        avgViews: "95K"
      }
    ];

    setCompetitors(mockCompetitors);
    setTrendingContent(mockTrending);
  }, [selectedNiche]);

  const niches = [
    { value: "fitness", label: "Fitness & Health" },
    { value: "beauty", label: "Beauty & Skincare" },
    { value: "lifestyle", label: "Lifestyle" },
    { value: "tech", label: "Tech Reviews" },
    { value: "fashion", label: "Fashion" }
  ];

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getGrowthColor = (growth: string) => {
    const value = parseInt(growth.replace('%', '').replace('+', ''));
    if (value >= 15) return "text-green-600";
    if (value >= 8) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
            🔍 Competitor Analysis
          </h1>
          <p className="text-gray-600">
            Monitor competitors, discover trending content, and stay ahead of the curve
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <MaterialIcon name="refresh-cw" size={16} className="mr-2" />
            Refresh Data
          </Button>
          <Button className="bg-green-600 hover:bg-green-700">
            <MaterialIcon name="circle-plus" size={16} className="mr-2" />
            Add Competitor
          </Button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search competitors or hashtags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <select
          value={selectedNiche}
          onChange={(e) => setSelectedNiche(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md bg-white"
        >
          {niches.map(niche => (
            <option key={niche.value} value={niche.value}>{niche.label}</option>
          ))}
        </select>
      </div>

      {/* Competitor Leaderboard */}
      <Card>
        <CardHeader>
          <CardTitle>Top Competitors in {niches.find(n => n.value === selectedNiche)?.label}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {competitors.map((competitor, index) => (
              <div key={competitor.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="font-bold text-gray-600">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{competitor.username}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">{competitor.platform}</Badge>
                      <span className="text-sm text-gray-600">{competitor.followers} followers</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-2xl font-bold ${getScoreColor(competitor.score)}`}>
                    {competitor.score}
                  </div>
                  <p className="text-xs text-gray-600">Influence Score</p>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{competitor.avgViews}</div>
                  <p className="text-xs text-gray-600">Avg Views</p>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{competitor.engagementRate}%</div>
                  <p className="text-xs text-gray-600">Engagement</p>
                </div>
                <div className="text-right">
                  <div className={`font-semibold ${getGrowthColor(competitor.growth)}`}>
                    {competitor.growth}
                  </div>
                  <p className="text-xs text-gray-600">Growth</p>
                </div>
                <Button size="sm" variant="outline">
                  <MaterialIcon name="eye" size={16} className="mr-1" />
                  Analyze
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Trending Content Analysis */}
      <div className="grid lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Trending Hashtags</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trendingContent.map(trend => (
                <div key={trend.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-blue-600">{trend.hashtag}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="text-xs">{trend.platform}</Badge>
                      <span className="text-sm text-gray-600">{trend.posts} posts</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`font-semibold ${getGrowthColor(trend.growth)}`}>
                      {trend.growth}
                    </div>
                    <p className="text-xs text-gray-600">{trend.avgViews} avg views</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content Opportunities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                <h4 className="font-semibold text-green-900 mb-2">🎯 High Opportunity</h4>
                <p className="text-green-800 text-sm mb-2">
                  "Morning routine" content is trending but has low competition in your follower range.
                </p>
                <Button size="sm" className="bg-green-600 hover:bg-green-700">
                  Create Script
                </Button>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-semibold text-blue-900 mb-2">📈 Growing Trend</h4>
                <p className="text-blue-800 text-sm mb-2">
                  "Healthy meal prep" is gaining momentum. Get ahead of the curve.
                </p>
                <Button size="sm" variant="outline">
                  Learn More
                </Button>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                <h4 className="font-semibold text-yellow-900 mb-2">⚠️ Saturated Market</h4>
                <p className="text-yellow-800 text-sm mb-2">
                  "Gym workout" content is highly competitive. Consider niche alternatives.
                </p>
                <Button size="sm" variant="outline">
                  See Alternatives
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI-Powered Insights */}
      <Card className="border-purple-200 bg-purple-50">
        <CardHeader>
          <CardTitle className="flex items-center">
            <MaterialIcon name="brain" size={24} className="text-purple-600 mr-2" />
            AI Competitive Intelligence
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Market Position</h4>
              <p className="text-purple-800 text-sm">
                You're in the top 25% of creators in your niche. Focus on consistency to break into top 10%.
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Content Gaps</h4>
              <p className="text-purple-800 text-sm">
                Competitors are missing educational content about nutrition basics. High opportunity area.
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Best Posting Times</h4>
              <p className="text-purple-800 text-sm">
                Your competitors post at 2-4 PM. Try 6-8 PM for less competition and higher engagement.
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Collaboration Opportunities</h4>
              <p className="text-purple-800 text-sm">
                @healthylifestyle has similar audience. Consider collaboration for mutual growth.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
