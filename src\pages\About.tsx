
import { Navigation } from "@/components/Navigation";
import { FooterSection } from "@/components/sections/FooterSection";
import { useState } from "react";
import { motion } from "framer-motion";

const About = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white font-inter">
      <Navigation 
        isMobileNavOpen={isMobileNavOpen} 
        onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)} 
      />

      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl font-manrope font-bold text-center mb-8 text-gray-900">
            About UGC Kit Pro
          </h1>
          
          <div className="prose prose-lg mx-auto text-gray-600">
            <p className="text-xl text-center mb-12">
              We're on a mission to democratize viral content creation for creators everywhere.
            </p>

            <div className="grid md:grid-cols-2 gap-12 my-16">
              <div>
                <h2 className="text-2xl font-manrope font-bold text-gray-900 mb-4">Our Story</h2>
                <p>
                  UGC Kit Pro was born from the frustration of watching talented creators struggle 
                  with the technical side of content creation. We believe that great ideas shouldn't 
                  be held back by complex tools or steep learning curves.
                </p>
                <p>
                  Our team of content creators, developers, and AI specialists came together to build 
                  the ultimate toolkit that makes viral content creation accessible to everyone.
                </p>
              </div>
              
              <div>
                <h2 className="text-2xl font-manrope font-bold text-gray-900 mb-4">Our Mission</h2>
                <p>
                  To empower every creator with AI-powered tools that turn ideas into viral content 
                  in seconds, not hours. We're building the future of content creation where 
                  creativity meets efficiency.
                </p>
                <p>
                  Whether you're a solo creator or part of a team, UGC Kit Pro provides the 
                  automation and templates you need to scale your content strategy.
                </p>
              </div>
            </div>

            <div className="text-center bg-gray-50 p-8 rounded-lg">
              <h2 className="text-2xl font-manrope font-bold text-gray-900 mb-4">Join Our Community</h2>
              <p className="text-lg">
                Join thousands of creators who are already using UGC Kit Pro to create 
                viral content and grow their audience.
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      <FooterSection />
    </div>
  );
};

export default About;
