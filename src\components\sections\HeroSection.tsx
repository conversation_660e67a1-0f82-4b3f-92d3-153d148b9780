
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";
import { EnhancedEmailForm } from "@/components/EnhancedEmailForm";

interface HeroSectionProps {
  onEmailSubmit: (email: string) => void;
}

export const HeroSection = ({ onEmailSubmit }: HeroSectionProps) => {
  return (
    <section className="relative overflow-hidden bg-white py-20">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
          >
            <Badge className="mb-6 bg-green-50 text-green-700 border-green-200 px-4 py-2 text-sm font-medium">
              🔥 FREE Starter Pack Available
            </Badge>
            <h1 className="text-5xl md:text-6xl font-manrope font-bold mb-6 leading-tight text-gray-900">
              Your Next Viral Post Is Just{" "}
              <span className="text-green-500">30 Seconds Away</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              The Ultimate AI-Powered UGC Toolkit to Create High-Converting Scripts, Hooks & Captions — Without Writing a Word.
            </p>
            
            <div className="mb-12">
              <EnhancedEmailForm onSubmit={onEmailSubmit} />
            </div>

            <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <MaterialIcon name="star" className="text-yellow-400" size={16} />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center gap-2">
                <MaterialIcon name="group" size={16} />
                <span>1,000+ Creators</span>
              </div>
              <div className="flex items-center gap-2">
                <MaterialIcon name="check_circle" className="text-green-500" size={16} />
                <span>No Credit Card Required</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
