
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ContentCalendarProps {
  userPlan: any;
}

export const ContentCalendar = ({ userPlan }: ContentCalendarProps) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [scheduledContent, setScheduledContent] = useState<any[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  useEffect(() => {
    // Load scheduled content
    const mockScheduled = [
      {
        id: 1,
        date: new Date(2024, 4, 15),
        title: "Morning Routine Video",
        platform: "TikTok",
        status: "scheduled",
        type: "video-script"
      },
      {
        id: 2,
        date: new Date(2024, 4, 17),
        title: "Product Review",
        platform: "Instagram",
        status: "draft",
        type: "testimonial"
      },
      {
        id: 3,
        date: new Date(2024, 4, 20),
        title: "Transformation Story",
        platform: "YouTube",
        status: "published",
        type: "ad-copy"
      }
    ];
    setScheduledContent(mockScheduled);
  }, []);

  const getDaysInMonth = (date: Date) => {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(date.getFullYear(), date.getMonth(), day));
    }
    
    return days;
  };

  const getContentForDate = (date: Date | null) => {
    if (!date) return [];
    return scheduledContent.filter(content => 
      content.date.toDateString() === date.toDateString()
    );
  };

  const navigateMonth = (direction: number) => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + direction, 1));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'published': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
            📅 Content Calendar
          </h1>
          <p className="text-gray-600">
            Plan, schedule, and track your UGC content across all platforms
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <MaterialIcon name="calendar-check" size={16} className="mr-2" />
            Templates
          </Button>
          <Button className="bg-green-600 hover:bg-green-700">
            <MaterialIcon name="circle-plus" size={16} className="mr-2" />
            Schedule Content
          </Button>
        </div>
      </div>

      {/* Calendar Navigation */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth(-1)}
              >
                <MaterialIcon name="chevron-left" size={16} />
              </Button>
              <span className="text-xl font-semibold">
                {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth(1)}
              >
                <MaterialIcon name="chevron-right" size={16} />
              </Button>
            </CardTitle>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">Month</Button>
              <Button variant="outline" size="sm">Week</Button>
              <Button variant="outline" size="sm">Day</Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-2">
            {/* Day headers */}
            {dayNames.map(day => (
              <div key={day} className="p-2 text-center font-semibold text-gray-600 text-sm">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {getDaysInMonth(currentMonth).map((date, index) => {
              const content = getContentForDate(date);
              const isToday = date && date.toDateString() === new Date().toDateString();
              
              return (
                <div
                  key={index}
                  className={`min-h-24 p-2 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 ${
                    isToday ? 'bg-blue-50 border-blue-300' : ''
                  } ${!date ? 'opacity-30' : ''}`}
                  onClick={() => date && setSelectedDate(date)}
                >
                  {date && (
                    <>
                      <div className={`text-sm font-medium ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
                        {date.getDate()}
                      </div>
                      <div className="space-y-1 mt-1">
                        {content.slice(0, 2).map(item => (
                          <div
                            key={item.id}
                            className="text-xs p-1 rounded bg-green-100 text-green-800 truncate"
                          >
                            {item.title}
                          </div>
                        ))}
                        {content.length > 2 && (
                          <div className="text-xs text-gray-500">
                            +{content.length - 2} more
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Content Overview */}
      <div className="grid md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>This Week's Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {scheduledContent.slice(0, 4).map(item => (
                <div key={item.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-sm">{item.title}</h4>
                    <p className="text-xs text-gray-600">{item.platform}</p>
                  </div>
                  <Badge className={getStatusColor(item.status)}>
                    {item.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Content Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Scheduled</span>
                <span className="font-semibold">12 posts</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Published</span>
                <span className="font-semibold">8 posts</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Drafts</span>
                <span className="font-semibold">5 posts</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">This Month</span>
                <span className="font-semibold text-green-600">25 posts</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <MaterialIcon name="calendar-plus" size={16} className="mr-2" />
                Schedule New Post
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MaterialIcon name="repeat" size={16} className="mr-2" />
                Bulk Upload
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MaterialIcon name="download" size={16} className="mr-2" />
                Export Calendar
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <MaterialIcon name="settings" size={16} className="mr-2" />
                Auto-Schedule
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Platform Integration Status */}
      <Card>
        <CardHeader>
          <CardTitle>Platform Integrations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-4 gap-4">
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <MaterialIcon name="video" size={32} className="mx-auto mb-2 text-pink-600" />
              <h4 className="font-medium mb-1">TikTok</h4>
              <Badge className="bg-green-100 text-green-800">Connected</Badge>
              <p className="text-xs text-gray-600 mt-1">Auto-posting enabled</p>
            </div>
            
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <MaterialIcon name="camera" size={32} className="mx-auto mb-2 text-purple-600" />
              <h4 className="font-medium mb-1">Instagram</h4>
              <Badge className="bg-green-100 text-green-800">Connected</Badge>
              <p className="text-xs text-gray-600 mt-1">Stories & Reels</p>
            </div>
            
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <MaterialIcon name="play" size={32} className="mx-auto mb-2 text-red-600" />
              <h4 className="font-medium mb-1">YouTube</h4>
              <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
              <p className="text-xs text-gray-600 mt-1">Setup required</p>
            </div>
            
            <div className="text-center p-4 border border-gray-200 rounded-lg">
              <MaterialIcon name="facebook" size={32} className="mx-auto mb-2 text-blue-600" />
              <h4 className="font-medium mb-1">Facebook</h4>
              <Badge variant="outline">Not Connected</Badge>
              <p className="text-xs text-gray-600 mt-1">Available in Pro+</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
