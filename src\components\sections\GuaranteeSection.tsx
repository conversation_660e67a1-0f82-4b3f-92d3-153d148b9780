
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";

export const GuaranteeSection = () => {
  return (
    <section className="py-20 bg-green-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center max-w-4xl mx-auto"
        >
          <Card className="p-8 border-green-200 bg-white">
            <div className="flex items-center justify-center gap-4 mb-6">
              <MaterialIcon name="security" className="text-green-500" size={48} />
              <h2 className="text-3xl md:text-4xl font-manrope font-bold text-gray-900">
                Try UGC Kit Pro™ 100% Risk-Free
              </h2>
            </div>
            <p className="text-xl text-gray-700 mb-6">
              If you don't love it in 7 days, you'll get your money back — no questions asked.
            </p>
            <Badge className="bg-green-500 text-white text-lg px-6 py-2">
              7-Day Money-Back Guarantee
            </Badge>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};
