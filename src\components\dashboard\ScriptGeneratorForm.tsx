
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MaterialIcon } from "@/components/MaterialIcon";
import { ScriptFormData, contentTypes, tones, platforms } from "./ScriptGeneratorTypes";

interface ScriptGeneratorFormProps {
  formData: ScriptFormData;
  isGenerating: boolean;
  onFormDataChange: (data: ScriptFormData) => void;
  onGenerate: () => void;
}

export const ScriptGeneratorForm = ({
  formData,
  isGenerating,
  onFormDataChange,
  onGenerate
}: ScriptGeneratorFormProps) => {
  const handleInputChange = (field: keyof ScriptFormData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Script Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label>Content Type</Label>
          <select
            value={formData.contentType}
            onChange={(e) => handleInputChange('contentType', e.target.value)}
            className="w-full mt-1 p-2 border border-gray-300 rounded-md"
            aria-label="Content Type"
          >
            {contentTypes.map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>

        <div>
          <Label htmlFor="niche">Niche/Topic</Label>
          <Input
            id="niche"
            placeholder="e.g., fitness, beauty, tech, lifestyle"
            value={formData.niche}
            onChange={(e) => handleInputChange('niche', e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="product">Product Description (Optional)</Label>
          <Input
            id="product"
            placeholder="Brief description of the product/service"
            value={formData.productDescription}
            onChange={(e) => handleInputChange('productDescription', e.target.value)}
          />
        </div>

        <div>
          <Label>Tone</Label>
          <select
            value={formData.tone}
            onChange={(e) => handleInputChange('tone', e.target.value)}
            className="w-full mt-1 p-2 border border-gray-300 rounded-md"
            aria-label="Tone"
          >
            {tones.map(tone => (
              <option key={tone.value} value={tone.value}>{tone.label}</option>
            ))}
          </select>
        </div>

        <div>
          <Label>Platform Focus</Label>
          <select
            value={formData.platform}
            onChange={(e) => handleInputChange('platform', e.target.value)}
            className="w-full mt-1 p-2 border border-gray-300 rounded-md"
            aria-label="Platform Focus"
          >
            {platforms.map(platform => (
              <option key={platform.value} value={platform.value}>{platform.label}</option>
            ))}
          </select>
        </div>

        <div className="flex justify-end mt-6">
          <Button
            onClick={onGenerate}
            disabled={isGenerating}
            className="w-full bg-green-500 hover:bg-green-600 flex items-center justify-center px-6 py-3"
          >
            {isGenerating ? (
              <>
                <MaterialIcon name="settings" size={20} className="mr-2 animate-spin" />
                Generating Script...
              </>
            ) : (
              <>
                <MaterialIcon name="zap" size={20} className="mr-2" />
                Generate UGC Script
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
