
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { MaterialIcon } from "@/components/MaterialIcon";

export const TestimonialsSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const testimonials = [
    {
      quote: "I got 3 clients from 3 pitches using scripts from UGC Kit Pro. Game changer.",
      author: "<PERSON>",
      role: "UGC Freelancer"
    },
    {
      quote: "I replaced my $1,500/month copywriter with this tool. It's that good.",
      author: "<PERSON><PERSON>",
      role: "Shopify Owner"
    },
    {
      quote: "I use it for 2 businesses. Zero effort. Viral content every week.",
      author: "Max P.",
      role: "Agency Owner"
    }
  ];

  return (
    <section className="py-20 bg-gray-50" id="testimonials">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-4xl md:text-5xl font-manrope font-bold text-center mb-16 text-gray-900"
        >
          What Our Users Say
        </motion.h2>
        
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div key={index} variants={fadeInUp}>
              <Card className="p-6 h-full border-0 shadow-sm">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <MaterialIcon key={i} name="star" className="text-yellow-400" size={16} />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-4 italic leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>
                <div className="text-sm">
                  <div className="font-semibold text-gray-900">{testimonial.author}</div>
                  <div className="text-gray-600">{testimonial.role}</div>
                </div>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};
