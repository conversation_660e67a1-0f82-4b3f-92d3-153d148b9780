
import { motion } from "framer-motion";
import { MaterialIcon } from "@/components/MaterialIcon";

export const StatsSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto text-center"
        >
          <motion.div variants={fadeInUp}>
            <div className="text-4xl font-manrope font-bold text-gray-900 mb-2">75.2%</div>
            <div className="text-gray-600">Average daily activity</div>
          </motion.div>
          <motion.div variants={fadeInUp}>
            <div className="text-4xl font-manrope font-bold text-gray-900 mb-2">~20k</div>
            <div className="text-gray-600">Average daily users</div>
          </motion.div>
          <motion.div variants={fadeInUp}>
            <div className="flex items-center justify-center gap-1 mb-2">
              {[...Array(4)].map((_, i) => (
                <MaterialIcon key={i} name="star" className="text-yellow-400" size={20} />
              ))}
              <MaterialIcon name="star_half" className="text-yellow-400" size={20} />
              <span className="ml-2 text-2xl font-manrope font-bold text-gray-900">4.5</span>
            </div>
            <div className="text-gray-600">Average user rating</div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
