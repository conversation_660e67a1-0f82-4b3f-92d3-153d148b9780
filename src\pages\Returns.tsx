
import { Navigation } from "@/components/Navigation";
import { FooterSection } from "@/components/sections/FooterSection";
import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MaterialIcon } from "@/components/MaterialIcon";

const Returns = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white font-inter">
      <Navigation 
        isMobileNavOpen={isMobileNavOpen} 
        onMobileNavToggle={() => setIsMobileNavOpen(!isMobileNavOpen)} 
      />

      <div className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-4xl md:text-5xl font-manrope font-bold text-center mb-8 text-gray-900">
            Return Policy
          </h1>
          
          <div className="mb-8">
            <Card className="border-green-500 border-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MaterialIcon name="verified" className="text-green-500" />
                  <span>30-Day Money-Back Guarantee</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-lg text-gray-600">
                  We're confident you'll love UGC Kit Pro. If you're not completely satisfied 
                  within the first 30 days, we'll refund your money, no questions asked.
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="prose prose-lg mx-auto text-gray-600">
            <p className="text-center text-sm text-gray-500 mb-8">
              Last updated: January 1, 2024
            </p>

            <h2>Refund Eligibility</h2>
            <p>
              You are eligible for a full refund within 30 days of your purchase if:
            </p>
            <ul>
              <li>You are not satisfied with the quality of our templates</li>
              <li>The product doesn't meet your expectations</li>
              <li>You experience technical issues that we cannot resolve</li>
              <li>You purchased the wrong plan by mistake</li>
            </ul>

            <h2>How to Request a Refund</h2>
            <p>
              To request a refund, simply:
            </p>
            <ol>
              <li>Contact our support <NAME_EMAIL></li>
              <li>Include your order number and reason for the refund</li>
              <li>We'll process your refund within 3-5 business days</li>
            </ol>

            <h2>Refund Process</h2>
            <p>
              Once your refund request is approved:
            </p>
            <ul>
              <li>You'll receive a confirmation email</li>
              <li>Your refund will be processed to the original payment method</li>
              <li>Bank processing may take 3-5 additional business days</li>
              <li>Your access to UGC Kit Pro will be revoked</li>
            </ul>

            <h2>Non-Refundable Items</h2>
            <p>
              The following items are not eligible for refunds:
            </p>
            <ul>
              <li>Purchases made more than 30 days ago</li>
              <li>Services that have been fully delivered and used extensively</li>
              <li>Custom work or personalized content</li>
            </ul>

            <h2>Plan Downgrades</h2>
            <p>
              If you want to downgrade your plan instead of requesting a full refund, 
              contact our support team. We can help you switch to a lower-tier plan 
              and prorate the difference.
            </p>

            <div className="mt-12 p-6 bg-gray-50 rounded-lg">
              <h3 className="font-manrope font-bold text-gray-900 mb-2">Need Help?</h3>
              <p>
                Before requesting a refund, consider reaching out to our support team. 
                We're here to help you get the most out of UGC Kit Pro and might be 
                able to resolve any issues you're experiencing.
                <br /><br />
                Email: <EMAIL>
                <br />
                Response time: Within 24 hours
              </p>
            </div>
          </div>
        </motion.div>
      </div>

      <FooterSection />
    </div>
  );
};

export default Returns;
