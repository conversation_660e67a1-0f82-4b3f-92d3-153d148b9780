
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Di<PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { MaterialIcon } from "@/components/MaterialIcon";
import { useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialView?: "signin" | "register" | "forgot";
}

export const AuthModal = ({ isOpen, onClose, initialView = "signin" }: AuthModalProps) => {
  const [currentView, setCurrentView] = useState<"signin" | "register" | "forgot">(initialView);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: ""
  });
  const navigate = useNavigate();

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSignIn = () => {
    // Mock authentication - bypass Supabase temporarily
    toast({
      title: "Welcome back!",
      description: "Successfully signed in."
    });
    onClose();
    navigate("/dashboard");
  };

  const handleRegister = () => {
    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match.",
        variant: "destructive"
      });
      return;
    }
    // Mock registration - bypass Supabase temporarily
    toast({
      title: "Account created!",
      description: "Welcome to UGC Kit Pro."
    });
    onClose();
    navigate("/dashboard");
  };

  const handleForgotPassword = () => {
    toast({
      title: "Reset link sent!",
      description: "A reset link has been sent to your email."
    });
    setCurrentView("signin");
  };

  const renderSignInView = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-4"
    >
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          placeholder="Enter your password"
          value={formData.password}
          onChange={(e) => handleInputChange("password", e.target.value)}
        />
      </div>
      <button
        onClick={() => setCurrentView("forgot")}
        className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
      >
        Forgot Password?
      </button>
      <Button onClick={handleSignIn} className="w-full bg-green-500 hover:bg-green-600">
        Sign In
      </Button>
      <p className="text-center text-sm text-gray-600">
        Don't have an account?{" "}
        <button
          onClick={() => setCurrentView("register")}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          Get Started
        </button>
      </p>
    </motion.div>
  );

  const renderRegisterView = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-4"
    >
      <div className="space-y-2">
        <Label htmlFor="fullName">Full Name</Label>
        <Input
          id="fullName"
          type="text"
          placeholder="Enter your full name"
          value={formData.fullName}
          onChange={(e) => handleInputChange("fullName", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <Input
          id="password"
          type="password"
          placeholder="Create a password"
          value={formData.password}
          onChange={(e) => handleInputChange("password", e.target.value)}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="confirmPassword">Confirm Password</Label>
        <Input
          id="confirmPassword"
          type="password"
          placeholder="Confirm your password"
          value={formData.confirmPassword}
          onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
        />
      </div>
      <Button onClick={handleRegister} className="w-full bg-green-500 hover:bg-green-600">
        Register
      </Button>
      <p className="text-center text-sm text-gray-600">
        Already have an account?{" "}
        <button
          onClick={() => setCurrentView("signin")}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          Sign In
        </button>
      </p>
    </motion.div>
  );

  const renderForgotPasswordView = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="space-y-4"
    >
      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          placeholder="Enter your email"
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
        />
      </div>
      <Button onClick={handleForgotPassword} className="w-full bg-green-500 hover:bg-green-600">
        Send Reset Link
      </Button>
      <button
        onClick={() => setCurrentView("signin")}
        className="w-full text-center text-sm text-blue-600 hover:text-blue-800 transition-colors"
      >
        Back to Sign In
      </button>
    </motion.div>
  );

  const getTitle = () => {
    switch (currentView) {
      case "signin":
        return "Welcome Back";
      case "register":
        return "Get Started";
      case "forgot":
        return "Reset Password";
      default:
        return "Welcome";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl font-manrope font-bold">
            {getTitle()}
          </DialogTitle>
        </DialogHeader>
        <AnimatePresence mode="wait">
          {currentView === "signin" && renderSignInView()}
          {currentView === "register" && renderRegisterView()}
          {currentView === "forgot" && renderForgotPasswordView()}
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
};
