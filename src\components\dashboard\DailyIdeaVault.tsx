
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";

interface DailyIdeaVaultProps {
  userPlan: any;
}

export const DailyIdeaVault = ({ userPlan }: DailyIdeaVaultProps) => {
  const [todayIdea, setTodayIdea] = useState<any>(null);
  const [savedIdeas, setSavedIdeas] = useState<any[]>([]);

  useEffect(() => {
    // Load today's idea and saved ideas
    const ideas = [
      {
        id: 1,
        title: "Before & After Transformation",
        description: "Show a dramatic before and after using your product over 30 days",
        category: "Lifestyle",
        platform: "TikTok",
        engagement: "High",
        date: new Date().toISOString()
      },
      {
        id: 2,
        title: "5 Things I Wish I Knew Before...",
        description: "Share insider tips about your niche that beginners always miss",
        category: "Educational",
        platform: "Instagram",
        engagement: "Medium",
        date: new Date().toISOString()
      }
    ];

    setTodayIdea(ideas[0]);
    setSavedIdeas(JSON.parse(localStorage.getItem('savedIdeas') || '[]'));
  }, []);

  const generateMoreIdeas = () => {
    const newIdeas = [
      {
        id: Date.now() + 1,
        title: "React to Your Old Content",
        description: "Find your worst performing post and react to it with what you'd do differently now",
        category: "Meta",
        platform: "TikTok",
        engagement: "High"
      },
      {
        id: Date.now() + 2,
        title: "Day in My Life as a [Your Niche]",
        description: "Show authentic behind-the-scenes of your daily routine",
        category: "Lifestyle",
        platform: "Instagram",
        engagement: "Medium"
      }
    ];

    setSavedIdeas(prev => [...newIdeas, ...prev]);
  };

  const saveIdea = (idea: any) => {
    const updated = [...savedIdeas, { ...idea, saved: true }];
    setSavedIdeas(updated);
    localStorage.setItem('savedIdeas', JSON.stringify(updated));
  };

  const quickGenerate = (idea: any) => {
    // This would integrate with the Script Generator
    localStorage.setItem('quickGenerateIdea', JSON.stringify(idea));
    window.dispatchEvent(new CustomEvent('quickGenerate', { detail: idea }));
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
          💡 Daily Idea Vault
        </h1>
        <p className="text-gray-600">
          Never run out of UGC ideas again - fresh inspiration delivered daily
        </p>
      </div>

      {/* Today's Featured Idea */}
      {todayIdea && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <MaterialIcon name="zap" size={24} className="text-green-600 mr-2" />
                Today's Featured Idea
              </CardTitle>
              <Badge className="bg-green-600 text-white">Fresh</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">{todayIdea.title}</h3>
                <p className="text-gray-600 mt-2">{todayIdea.description}</p>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <Badge variant="outline">{todayIdea.category}</Badge>
                <Badge variant="outline">{todayIdea.platform}</Badge>
                <Badge variant="outline" className="text-green-600">
                  {todayIdea.engagement} Engagement
                </Badge>
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={() => quickGenerate(todayIdea)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <MaterialIcon name="zap" size={16} className="mr-2" />
                  Quick Generate Script
                </Button>
                <Button
                  onClick={() => saveIdea(todayIdea)}
                  variant="outline"
                >
                  <MaterialIcon name="file-plus" size={16} className="mr-2" />
                  Save to Library
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Idea Generation Tools */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Generate More Ideas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600 text-sm">
                Need more inspiration? Generate 10 additional ideas based on trending topics and your niche.
              </p>
              <div className="flex justify-end">
                <Button
                  onClick={generateMoreIdeas}
                  className="w-full px-6 py-3"
                  variant="outline"
                >
                  <MaterialIcon name="circle-plus" size={16} className="mr-2" />
                  Generate 10 More Ideas
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Delivery Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-gray-600 text-sm">
                Get fresh ideas delivered to your email automatically.
              </p>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">Daily idea emails</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span className="text-sm">Weekly idea batches</span>
                </label>
              </div>
              <div className="flex justify-end">
                <Button variant="outline" className="w-full px-6 py-3">
                  <MaterialIcon name="settings" size={16} className="mr-2" />
                  Configure Delivery
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Saved Ideas Library */}
      <Card>
        <CardHeader>
          <CardTitle>Your Saved Ideas ({savedIdeas.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {savedIdeas.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {savedIdeas.slice(0, 6).map((idea) => (
                <div key={idea.id} className="border border-gray-200 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">{idea.title}</h4>
                  <p className="text-gray-600 text-sm mb-3 line-clamp-2">{idea.description}</p>
                  <div className="flex items-center space-x-2 mb-3">
                    <Badge variant="outline" className="text-xs">{idea.category}</Badge>
                    <Badge variant="outline" className="text-xs">{idea.platform}</Badge>
                  </div>
                  <Button
                    onClick={() => quickGenerate(idea)}
                    size="sm"
                    className="w-full"
                  >
                    Generate Script
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              <MaterialIcon name="file-text" size={48} className="mx-auto mb-4 opacity-50" />
              <p>No saved ideas yet</p>
              <p className="text-sm">Save ideas from the daily feed to build your library</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
