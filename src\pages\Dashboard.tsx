
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";
import { toast } from "@/hooks/use-toast";
import { ScriptGenerator } from "@/components/dashboard/ScriptGenerator";
import { DailyIdeaVault } from "@/components/dashboard/DailyIdeaVault";
import { PromptLibrary } from "@/components/dashboard/PromptLibrary";
import { ScriptLog } from "@/components/dashboard/ScriptLog";
import { DeliverySystem } from "@/components/dashboard/DeliverySystem";
import { BonusTools } from "@/components/dashboard/BonusTools";
import { AnalyticsDashboard } from "@/components/dashboard/AnalyticsDashboard";
import { ContentCalendar } from "@/components/dashboard/ContentCalendar";
import { CompetitorAnalysis } from "@/components/dashboard/CompetitorAnalysis";

// Import new modular components
import { SidebarNavigation } from "@/components/dashboard/SidebarNavigation";
import { Header } from "@/components/dashboard/Header";
import { SectionRenderer } from "@/components/dashboard/SectionRenderer";

const Dashboard = () => {
  const navigate = useNavigate();
  const [userPlan, setUserPlan] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [activeSection, setActiveSection] = useState("generator");

  useEffect(() => {
    // Load user data and plan
    const planData = JSON.parse(localStorage.getItem('userPlan') || 'null');
    const user = JSON.parse(localStorage.getItem('userData') || 'null');

    setUserPlan(planData);
    setUserData(user);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('userPlan');
    localStorage.removeItem('userData');
    toast({
      title: "Logged out",
      description: "You have been successfully logged out."
    });
    navigate("/");
  };

  const getPlanColor = () => {
    if (!userPlan) return "bg-gray-500";

    const colors = {
      starter: "bg-blue-500",
      pro: "bg-green-500",
      ultimate: "bg-purple-500"
    };

    return colors[userPlan.key as keyof typeof colors] || "bg-gray-500";
  };

  const sections = [
    { id: "generator", label: "Script Generator", icon: "edit" },
    { id: "ideas", label: "Daily Ideas", icon: "zap" },
    { id: "prompts", label: "Prompt Library", icon: "file-text" },
    { id: "analytics", label: "Analytics", icon: "trending-up" },
    { id: "calendar", label: "Content Calendar", icon: "calendar" },
    { id: "competitors", label: "Competitor Analysis", icon: "search" },
    { id: "history", label: "Script Log", icon: "file-minus" },
    { id: "delivery", label: "Auto Delivery", icon: "mail" },
    { id: "tools", label: "Bonus Tools", icon: "settings" }
  ];

  // Section rendering is now handled by SectionRenderer component

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header
        userPlan={userPlan}
        userData={userData}
        onLogout={handleLogout}
        getPlanColor={getPlanColor}
      />

      <div className="flex flex-1 overflow-hidden">
        <SidebarNavigation
          sections={sections}
          activeSection={activeSection}
          setActiveSection={setActiveSection}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto p-6">
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="max-w-6xl mx-auto"
          >
            <SectionRenderer id={activeSection} userPlan={userPlan} />
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
