
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MaterialIcon } from "@/components/MaterialIcon";
import { toast } from "@/hooks/use-toast";
import { ScriptGenerator } from "@/components/dashboard/ScriptGenerator";
import { DailyIdeaVault } from "@/components/dashboard/DailyIdeaVault";
import { PromptLibrary } from "@/components/dashboard/PromptLibrary";
import { ScriptLog } from "@/components/dashboard/ScriptLog";
import { DeliverySystem } from "@/components/dashboard/DeliverySystem";
import { BonusTools } from "@/components/dashboard/BonusTools";
import { AnalyticsDashboard } from "@/components/dashboard/AnalyticsDashboard";
import { ContentCalendar } from "@/components/dashboard/ContentCalendar";
import { CompetitorAnalysis } from "@/components/dashboard/CompetitorAnalysis";

const Dashboard = () => {
  const navigate = useNavigate();
  const [userPlan, setUserPlan] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [activeSection, setActiveSection] = useState("generator");

  useEffect(() => {
    // Load user data and plan
    const planData = JSON.parse(localStorage.getItem('userPlan') || 'null');
    const user = JSON.parse(localStorage.getItem('userData') || 'null');
    
    setUserPlan(planData);
    setUserData(user);
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('userPlan');
    localStorage.removeItem('userData');
    toast({
      title: "Logged out",
      description: "You have been successfully logged out."
    });
    navigate("/");
  };

  const getPlanColor = () => {
    if (!userPlan) return "bg-gray-500";
    
    const colors = {
      starter: "bg-blue-500",
      pro: "bg-green-500",
      ultimate: "bg-purple-500"
    };

    return colors[userPlan.key as keyof typeof colors] || "bg-gray-500";
  };

  const sections = [
    { id: "generator", label: "Script Generator", icon: "edit" },
    { id: "ideas", label: "Daily Ideas", icon: "zap" },
    { id: "prompts", label: "Prompt Library", icon: "file-text" },
    { id: "analytics", label: "Analytics", icon: "trending-up" },
    { id: "calendar", label: "Content Calendar", icon: "calendar" },
    { id: "competitors", label: "Competitor Analysis", icon: "search" },
    { id: "history", label: "Script Log", icon: "file-minus" },
    { id: "delivery", label: "Auto Delivery", icon: "mail" },
    { id: "tools", label: "Bonus Tools", icon: "settings" }
  ];

  const renderActiveSection = () => {
    switch (activeSection) {
      case "generator":
        return <ScriptGenerator userPlan={userPlan} />;
      case "ideas":
        return <DailyIdeaVault userPlan={userPlan} />;
      case "prompts":
        return <PromptLibrary userPlan={userPlan} />;
      case "analytics":
        return <AnalyticsDashboard userPlan={userPlan} />;
      case "calendar":
        return <ContentCalendar userPlan={userPlan} />;
      case "competitors":
        return <CompetitorAnalysis userPlan={userPlan} />;
      case "history":
        return <ScriptLog userPlan={userPlan} />;
      case "delivery":
        return <DeliverySystem userPlan={userPlan} />;
      case "tools":
        return <BonusTools userPlan={userPlan} />;
      default:
        return <ScriptGenerator userPlan={userPlan} />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50 flex-shrink-0">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">U</span>
              </div>
              <div className="flex flex-col">
                <span className="font-manrope font-bold text-xl text-gray-900">UGC Kit Pro</span>
                <span className="text-sm text-gray-500 hidden md:block">Personal UGC Command Center</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {userPlan && (
                <Badge className={`${getPlanColor()} text-white text-xs px-3 py-1 font-medium`}>
                  {userPlan.name}
                </Badge>
              )}
              <span className="text-gray-600 hidden md:block font-medium">
                Welcome, {userData?.fullName || "User"}!
              </span>
              <Button variant="outline" onClick={handleLogout} size="sm">
                <MaterialIcon name="log-out" size={16} className="mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Navigation */}
        <div className="w-64 bg-white border-r border-gray-200 flex-shrink-0 overflow-y-auto">
          <div className="p-4">
            <nav className="space-y-2">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-200 group text-sm font-medium ${
                    activeSection === section.id
                      ? "bg-green-50 text-green-700 border border-green-200 shadow-sm"
                      : "text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <MaterialIcon 
                    name={section.icon} 
                    size={18} 
                    className={`flex-shrink-0 ${
                      activeSection === section.id ? "text-green-600" : "text-gray-500 group-hover:text-gray-700"
                    }`}
                  />
                  <span className="truncate">{section.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="w-full max-w-none"
              >
                {renderActiveSection()}
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
