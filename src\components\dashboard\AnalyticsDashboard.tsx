
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MaterialIcon } from "@/components/MaterialIcon";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";

interface AnalyticsDashboardProps {
  userPlan: any;
}

export const AnalyticsDashboard = ({ userPlan }: AnalyticsDashboardProps) => {
  const [timeRange, setTimeRange] = useState("7days");
  const [selectedMetric, setSelectedMetric] = useState("engagement");
  const [analyticsData, setAnalyticsData] = useState<any>(null);

  useEffect(() => {
    // Simulate loading analytics data
    const mockData = {
      totalViews: 125000,
      engagementRate: 8.4,
      conversionRate: 2.1,
      topPerformingContent: [
        { id: 1, title: "Fitness Transformation", views: 25000, engagement: 12.5 },
        { id: 2, title: "Beauty Routine", views: 18000, engagement: 9.8 },
        { id: 3, title: "Tech Review", views: 15000, engagement: 7.2 }
      ],
      platformPerformance: {
        tiktok: { views: 45000, engagement: 9.2 },
        instagram: { views: 38000, engagement: 7.8 },
        youtube: { views: 42000, engagement: 8.1 }
      },
      audienceInsights: {
        topAge: "18-24",
        topGender: "Female (65%)",
        topLocation: "United States",
        peakHours: "6-8 PM"
      }
    };
    setAnalyticsData(mockData);
  }, [timeRange]);

  const metrics = [
    { value: "engagement", label: "Engagement Rate", icon: "trending-up" },
    { value: "reach", label: "Reach", icon: "users" },
    { value: "conversion", label: "Conversion", icon: "target" },
    { value: "retention", label: "Retention", icon: "refresh-cw" }
  ];

  const timeRanges = [
    { value: "7days", label: "Last 7 Days" },
    { value: "30days", label: "Last 30 Days" },
    { value: "90days", label: "Last 3 Months" },
    { value: "1year", label: "Last Year" }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-manrope font-bold text-gray-900 mb-2">
            📊 AI Analytics Dashboard
          </h1>
          <p className="text-gray-600">
            Track performance, optimize content, and grow your UGC business
          </p>
        </div>
        <div className="flex space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-md bg-white"
          >
            {timeRanges.map(range => (
              <option key={range.value} value={range.value}>{range.label}</option>
            ))}
          </select>
          <Button className="bg-green-600 hover:bg-green-700">
            <MaterialIcon name="circle-arrow-down" size={16} className="mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">125K</p>
                <p className="text-green-600 text-sm">↗ +15% vs last period</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <MaterialIcon name="eye" size={24} className="text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">Engagement Rate</p>
                <p className="text-2xl font-bold text-gray-900">8.4%</p>
                <p className="text-green-600 text-sm">↗ +2.1% vs last period</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <MaterialIcon name="heart" size={24} className="text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">Conversion Rate</p>
                <p className="text-2xl font-bold text-gray-900">2.1%</p>
                <p className="text-red-600 text-sm">↘ -0.3% vs last period</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <MaterialIcon name="target" size={24} className="text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 text-sm">Revenue Generated</p>
                <p className="text-2xl font-bold text-gray-900">$3,240</p>
                <p className="text-green-600 text-sm">↗ +28% vs last period</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <MaterialIcon name="dollar-sign" size={24} className="text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <div className="grid lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Content Performance Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MaterialIcon name="bar-chart" size={48} className="text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600">Interactive chart showing engagement trends</p>
                <p className="text-sm text-gray-500">Peaks at 6-8 PM daily</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Platform Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="text-sm font-medium">TikTok</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">45K views</span>
                  <span className="text-green-600 text-sm ml-2">9.2% eng</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium">Instagram</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">38K views</span>
                  <span className="text-green-600 text-sm ml-2">7.8% eng</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium">YouTube</span>
                </div>
                <div className="text-right">
                  <span className="font-semibold">42K views</span>
                  <span className="text-green-600 text-sm ml-2">8.1% eng</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Content */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Content</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analyticsData?.topPerformingContent.map((content: any, index: number) => (
              <div key={content.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="font-bold text-green-600">#{index + 1}</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{content.title}</h4>
                    <p className="text-sm text-gray-600">{content.views.toLocaleString()} views</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className="bg-green-100 text-green-800">
                    {content.engagement}% engagement
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights & Recommendations */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center">
            <MaterialIcon name="brain" size={24} className="text-blue-600 mr-2" />
            AI-Powered Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">🎯 Optimization Opportunity</h4>
              <p className="text-blue-800 text-sm">
                Your fitness content performs 40% better than beauty content. Consider creating 2-3 more fitness-related scripts this week.
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">📈 Trending Topic Alert</h4>
              <p className="text-blue-800 text-sm">
                "Morning routines" is trending in your niche with 250% increased engagement. Generate scripts around this topic.
              </p>
            </div>
            <div className="bg-white p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">⏰ Best Posting Time</h4>
              <p className="text-blue-800 text-sm">
                Your audience is most active at 6-8 PM EST. Schedule your content during these hours for maximum reach.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
